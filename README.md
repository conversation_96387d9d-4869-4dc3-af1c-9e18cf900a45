# Company Portal

A comprehensive company portal with purchase order workflow management, user roles, and customer management system.

## Features

### 🏢 Platform Tiers
- **Silver**: Basic features with limited users and POs
- **Gold**: Advanced features with workflow automation
- **Platinum**: Enterprise features with unlimited access

### 👥 User Roles
- **Admin**: Full access to all features including user management
- **Read Only**: View-only access to all data
- **Read & Write**: Can create/edit but requires approval for actions

### 🛒 Purchase Order Workflow
- Create, edit, and manage purchase orders
- Multi-level approval workflow
- Status tracking (Draft → Pending → Approved/Rejected)
- Item-based PO creation with automatic calculations

### 👤 Customer Management
- **NN (New Normal)**: New customers with standard requirements
- **EN (Existing Normal)**: Existing customers with standard requirements  
- **EE (Existing Enterprise)**: Enterprise customers with renewal types:
  - License Renewal
  - Support Activity Renewal
  - Change Request (CR) Renewal

### 📊 Dashboard & Analytics
- Real-time statistics and metrics
- Recent purchase orders overview
- Customer distribution by type
- Platform feature overview

## Getting Started

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- No server setup required - runs entirely in the browser

### Installation
1. Download or clone the project files
2. Open `login.html` in your web browser
3. Use the demo credentials to log in

### Demo Credentials

| Role | Email | Password | Access Level |
|------|-------|----------|--------------|
| Admin | <EMAIL> | admin123 | Full access including user management |
| Manager | <EMAIL> | jane123 | Read & Write with approval workflow |
| Viewer | <EMAIL> | bob123 | Read-only access |

## File Structure

```
company-portal/
├── Company.html              # Main portal dashboard
├── login.html               # Login page
├── README.md                # This file
├── styles/
│   ├── main.css            # Main stylesheet
│   └── components.css      # Component-specific styles
└── js/
    ├── data.js             # Data structures and mock data
    ├── auth.js             # Authentication system
    ├── app.js              # Main application logic
    ├── purchase-orders.js  # PO management functionality
    ├── customer-management.js # Customer management
    └── user-management.js  # User administration
```

## Key Functionality

### Purchase Order Workflow
1. **Creation**: Users with write permissions can create new POs
2. **Approval**: Pending POs require approval from authorized users
3. **Tracking**: Full audit trail of approvals and status changes
4. **Filtering**: Filter POs by status, customer type, etc.

### Customer Types & Renewals
- **NN/EN Customers**: Standard customer management
- **EE Customers**: Enhanced with renewal type tracking:
  - License renewals for software licensing
  - Support activity renewals for ongoing support
  - CR renewals for change requests

### User Management (Admin Only)
- Add/edit/deactivate users
- Assign roles and platform tiers
- View user activity (placeholder)
- Role-based access control

### Platform Features by Tier

#### Silver
- Basic Purchase Orders
- Customer Management  
- Basic Reporting
- Email Support
- Max 10 users, 50 POs

#### Gold
- Advanced Purchase Orders
- Advanced Reporting
- Workflow Automation
- Priority Support
- API Access
- Max 50 users, 200 POs

#### Platinum
- Enterprise Purchase Orders
- Custom Reporting
- Advanced Workflow Automation
- Multi-level Approvals
- Dedicated Support
- Full API Access
- Custom Integrations
- Unlimited users and POs

## Technical Details

### Data Storage
- Uses browser localStorage for data persistence
- Mock data structure simulates real database
- Session management with automatic timeout

### Security Features
- Role-based access control
- Session timeout (30 minutes)
- Permission checking on all actions
- User activity tracking (placeholder)

### Responsive Design
- Mobile-friendly interface
- Adaptive layouts for different screen sizes
- Touch-friendly controls

## Browser Compatibility
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Development

### Adding New Features
1. Update data structures in `js/data.js`
2. Add UI components to relevant HTML files
3. Implement functionality in appropriate JS modules
4. Update CSS for styling

### Customization
- Modify platform tiers in `PLATFORMS` object
- Adjust user roles in `USER_ROLES` object
- Update customer types in `CUSTOMER_TYPES` object
- Customize styling in CSS files

## Future Enhancements

### Planned Features
- Real backend integration
- Email notifications
- Advanced reporting and analytics
- Document attachments for POs
- Bulk operations
- Export functionality
- Advanced search and filtering
- Integration APIs

### Security Improvements
- Password hashing
- Two-factor authentication
- Advanced session management
- Audit logging
- Data encryption

## Support

For questions or issues:
1. Check the demo credentials are correct
2. Ensure JavaScript is enabled in your browser
3. Clear browser cache if experiencing issues
4. Use browser developer tools to check for errors

## License

This is a demonstration project. Modify and use as needed for your requirements.

---

**Note**: This is a demo application with mock data. In a production environment, you would need:
- Secure backend API
- Database integration
- Proper authentication system
- Server-side validation
- Security measures (HTTPS, CSRF protection, etc.)
