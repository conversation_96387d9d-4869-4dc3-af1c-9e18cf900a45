// Purchase Order Charts and Analytics

// Chart instances
let statusChart = null;
let trendsChart = null;
let customerTypeChart = null;
let topCustomersChart = null;
let dashboardStatusChart = null;

// Chart colors
const chartColors = {
    primary: '#3b82f6',
    secondary: '#8b5cf6',
    success: '#10b981',
    warning: '#f59e0b',
    danger: '#ef4444',
    info: '#06b6d4',
    gray: '#6b7280'
};

// Initialize all charts
function initializePOCharts() {
    initializeStatusChart();
    initializeTrendsChart();
    initializeCustomerTypeChart();
    initializeTopCustomersChart();
}

// Initialize dashboard chart
function initializeDashboardChart() {
    initializeDashboardStatusChart();
}

// Status Distribution Chart (Pie Chart)
function initializeStatusChart() {
    const ctx = document.getElementById('statusChart');
    if (!ctx) return;

    const statusData = getPOStatusData();
    
    if (statusChart) {
        statusChart.destroy();
    }

    statusChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: statusData.labels,
            datasets: [{
                data: statusData.values,
                backgroundColor: [
                    chartColors.warning,  // pending
                    chartColors.success,  // approved
                    chartColors.gray,     // draft
                    chartColors.danger    // rejected
                ],
                borderWidth: 2,
                borderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

// Monthly Trends Chart (Line Chart)
function initializeTrendsChart() {
    const ctx = document.getElementById('trendsChart');
    if (!ctx) return;

    const trendsData = getPOTrendsData();
    
    if (trendsChart) {
        trendsChart.destroy();
    }

    trendsChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: trendsData.labels,
            datasets: [{
                label: 'Purchase Orders',
                data: trendsData.counts,
                borderColor: chartColors.primary,
                backgroundColor: chartColors.primary + '20',
                fill: true,
                tension: 0.4
            }, {
                label: 'Total Amount ($)',
                data: trendsData.amounts,
                borderColor: chartColors.success,
                backgroundColor: chartColors.success + '20',
                fill: false,
                tension: 0.4,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Number of POs'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Amount ($)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                }
            }
        }
    });
}

// Customer Type Chart (Bar Chart)
function initializeCustomerTypeChart() {
    const ctx = document.getElementById('customerTypeChart');
    if (!ctx) return;

    const customerTypeData = getPOCustomerTypeData();
    
    if (customerTypeChart) {
        customerTypeChart.destroy();
    }

    customerTypeChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['NN Customers', 'EN Customers', 'EE Customers'],
            datasets: [{
                label: 'Total Amount ($)',
                data: customerTypeData.amounts,
                backgroundColor: [
                    chartColors.primary,
                    chartColors.secondary,
                    chartColors.warning
                ],
                borderColor: [
                    chartColors.primary,
                    chartColors.secondary,
                    chartColors.warning
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.label}: $${context.parsed.y.toLocaleString()}`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Amount ($)'
                    },
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });
}

// Top Customers Chart (Horizontal Bar Chart)
function initializeTopCustomersChart() {
    const ctx = document.getElementById('topCustomersChart');
    if (!ctx) return;

    const topCustomersData = getTopCustomersData();
    
    if (topCustomersChart) {
        topCustomersChart.destroy();
    }

    topCustomersChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: topCustomersData.labels,
            datasets: [{
                label: 'Total PO Value ($)',
                data: topCustomersData.values,
                backgroundColor: chartColors.info,
                borderColor: chartColors.info,
                borderWidth: 1
            }]
        },
        options: {
            indexAxis: 'y',
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `Total: $${context.parsed.x.toLocaleString()}`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Total Value ($)'
                    },
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });
}

// Data processing functions
function getPOStatusData() {
    const pos = getPurchaseOrders();
    const statusCounts = {
        pending: 0,
        approved: 0,
        draft: 0,
        rejected: 0
    };

    pos.forEach(po => {
        if (statusCounts.hasOwnProperty(po.status)) {
            statusCounts[po.status]++;
        }
    });

    return {
        labels: ['Pending', 'Approved', 'Draft', 'Rejected'],
        values: [statusCounts.pending, statusCounts.approved, statusCounts.draft, statusCounts.rejected]
    };
}

function getPOTrendsData() {
    const pos = getPurchaseOrders();
    const months = [];
    const counts = [];
    const amounts = [];

    // Get last 6 months
    for (let i = 5; i >= 0; i--) {
        const date = new Date();
        date.setMonth(date.getMonth() - i);
        const monthYear = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
        months.push(monthYear);

        const monthPOs = pos.filter(po => {
            const poDate = new Date(po.created);
            return poDate.getMonth() === date.getMonth() && poDate.getFullYear() === date.getFullYear();
        });

        counts.push(monthPOs.length);
        amounts.push(monthPOs.reduce((sum, po) => sum + po.amount, 0));
    }

    return {
        labels: months,
        counts: counts,
        amounts: amounts
    };
}

function getPOCustomerTypeData() {
    const pos = getPurchaseOrders();
    const typeAmounts = { NN: 0, EN: 0, EE: 0 };

    pos.forEach(po => {
        if (typeAmounts.hasOwnProperty(po.customerType)) {
            typeAmounts[po.customerType] += po.amount;
        }
    });

    return {
        amounts: [typeAmounts.NN, typeAmounts.EN, typeAmounts.EE]
    };
}

function getTopCustomersData(limit = 5) {
    const pos = getPurchaseOrders();
    const customerTotals = {};

    pos.forEach(po => {
        if (!customerTotals[po.customerName]) {
            customerTotals[po.customerName] = 0;
        }
        customerTotals[po.customerName] += po.amount;
    });

    const sorted = Object.entries(customerTotals)
        .sort(([,a], [,b]) => b - a)
        .slice(0, limit);

    return {
        labels: sorted.map(([name]) => name),
        values: sorted.map(([, value]) => value)
    };
}

// Chart update functions
function refreshCharts() {
    initializePOCharts();
    showNotification('Charts refreshed successfully!', 'success');
}

function updateTrendChart() {
    const period = document.getElementById('trendPeriod').value;
    // For now, just refresh the chart
    // In a real implementation, you would modify the data based on the period
    initializeTrendsChart();
}

function updateTopCustomersChart() {
    const count = parseInt(document.getElementById('topCustomersCount').value);

    const ctx = document.getElementById('topCustomersChart');
    if (!ctx) return;

    const topCustomersData = getTopCustomersData(count);

    if (topCustomersChart) {
        topCustomersChart.data.labels = topCustomersData.labels;
        topCustomersChart.data.datasets[0].data = topCustomersData.values;
        topCustomersChart.update();
    }
}

// Dashboard Status Chart (Smaller version for dashboard)
function initializeDashboardStatusChart() {
    const ctx = document.getElementById('dashboardStatusChart');
    if (!ctx) return;

    const statusData = getPOStatusData();

    if (dashboardStatusChart) {
        dashboardStatusChart.destroy();
    }

    dashboardStatusChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: statusData.labels,
            datasets: [{
                data: statusData.values,
                backgroundColor: [
                    chartColors.warning,  // pending
                    chartColors.success,  // approved
                    chartColors.gray,     // draft
                    chartColors.danger    // rejected
                ],
                borderWidth: 2,
                borderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 15,
                        usePointStyle: true,
                        font: {
                            size: 11
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}
