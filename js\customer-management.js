// Customer management functionality

// Add new customer
function addCustomer() {
    if (!auth.canWrite()) {
        showNotification('You do not have permission to add customers.', 'error');
        return;
    }
    
    const modalBody = document.getElementById('modalBody');
    
    modalBody.innerHTML = `
        <h2>Add New Customer</h2>
        <form id="customerForm" onsubmit="saveCustomer(event)">
            <div class="form-row">
                <div class="form-group">
                    <label for="customerName">Customer Name:</label>
                    <input type="text" id="customerName" required placeholder="Enter customer name">
                </div>
                <div class="form-group">
                    <label for="customerEmail">Email:</label>
                    <input type="email" id="customerEmail" required placeholder="<EMAIL>">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="customerPhone">Phone:</label>
                    <input type="tel" id="customerPhone" required placeholder="******-0000">
                </div>
                <div class="form-group">
                    <label for="customerType">Customer Type:</label>
                    <select id="customerType" required onchange="toggleRenewalOptions()">
                        <option value="">Select Type</option>
                        <option value="NN">NN - New Normal</option>
                        <option value="EN">EN - Existing Normal</option>
                        <option value="EE">EE - Existing Enterprise</option>
                    </select>
                </div>
            </div>
            
            <div id="renewalOptions" class="renewal-options">
                <label>Renewal Types (for EE customers):</label>
                <div class="renewal-option">
                    <input type="checkbox" id="renewalLicense" value="license">
                    <label for="renewalLicense">License Renewal</label>
                </div>
                <div class="renewal-option">
                    <input type="checkbox" id="renewalSupport" value="support_activity">
                    <label for="renewalSupport">Support Activity Renewal</label>
                </div>
                <div class="renewal-option">
                    <input type="checkbox" id="renewalCR" value="cr">
                    <label for="renewalCR">Change Request (CR) Renewal</label>
                </div>
            </div>
            
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                <button type="submit" class="btn btn-primary">Add Customer</button>
            </div>
        </form>
    `;
    
    showModal();
}

// Toggle renewal options based on customer type
function toggleRenewalOptions() {
    const customerType = document.getElementById('customerType').value;
    const renewalOptions = document.getElementById('renewalOptions');
    
    if (customerType === 'EE') {
        renewalOptions.classList.add('show');
    } else {
        renewalOptions.classList.remove('show');
        // Clear all checkboxes
        const checkboxes = renewalOptions.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(cb => cb.checked = false);
    }
}

// Save customer
function saveCustomer(event) {
    event.preventDefault();
    
    const name = document.getElementById('customerName').value;
    const email = document.getElementById('customerEmail').value;
    const phone = document.getElementById('customerPhone').value;
    const type = document.getElementById('customerType').value;
    
    // Collect renewal types for EE customers
    let renewalTypes = [];
    if (type === 'EE') {
        const checkboxes = document.querySelectorAll('#renewalOptions input[type="checkbox"]:checked');
        renewalTypes = Array.from(checkboxes).map(cb => cb.value);
    }
    
    const newCustomer = {
        name,
        email,
        phone,
        type,
        renewalTypes
    };
    
    const savedCustomer = addCustomer(newCustomer);
    saveToLocalStorage();
    
    closeModal();
    showNotification('Customer added successfully!', 'success');
    
    // Refresh the current view
    if (currentSection === 'customers') {
        loadCustomers();
    }
    loadDashboard(); // Update dashboard stats
}

// View customer details
function viewCustomer(customerId) {
    const customer = getCustomerById(customerId);
    if (!customer) {
        showNotification('Customer not found.', 'error');
        return;
    }
    
    // Get customer's purchase orders
    const customerPOs = getPurchaseOrders().filter(po => po.customerId === customerId);
    const totalSpent = customerPOs
        .filter(po => po.status === 'approved')
        .reduce((sum, po) => sum + po.amount, 0);
    
    const modalBody = document.getElementById('modalBody');
    
    modalBody.innerHTML = `
        <h2>Customer Details</h2>
        <div class="customer-details">
            <div class="form-row">
                <div class="form-group">
                    <label>Name:</label>
                    <div>${customer.name}</div>
                </div>
                <div class="form-group">
                    <label>Type:</label>
                    <span class="customer-type-badge customer-type-${customer.type.toLowerCase()}">
                        ${customer.type} - ${CUSTOMER_TYPES[customer.type].name}
                    </span>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label>Email:</label>
                    <div>${customer.email}</div>
                </div>
                <div class="form-group">
                    <label>Phone:</label>
                    <div>${customer.phone}</div>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label>Created:</label>
                    <div>${formatDate(customer.created)}</div>
                </div>
                <div class="form-group">
                    <label>Status:</label>
                    <div>${customer.active ? 'Active' : 'Inactive'}</div>
                </div>
            </div>
            
            ${customer.renewalTypes.length > 0 ? `
                <div class="form-group">
                    <label>Renewal Types:</label>
                    <div class="renewal-types">
                        ${customer.renewalTypes.map(type => 
                            `<span class="renewal-badge">${RENEWAL_TYPES[type]}</span>`
                        ).join('')}
                    </div>
                </div>
            ` : ''}
            
            <div class="form-group">
                <label>Purchase Order Summary:</label>
                <div class="customer-stats">
                    <div class="stat-item">
                        <strong>Total Orders:</strong> ${customerPOs.length}
                    </div>
                    <div class="stat-item">
                        <strong>Total Spent:</strong> $${totalSpent.toLocaleString()}
                    </div>
                    <div class="stat-item">
                        <strong>Pending Orders:</strong> ${customerPOs.filter(po => po.status === 'pending').length}
                    </div>
                </div>
            </div>
            
            ${customerPOs.length > 0 ? `
                <div class="form-group">
                    <label>Recent Purchase Orders:</label>
                    <table class="po-table">
                        <thead>
                            <tr>
                                <th>PO Number</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${customerPOs.slice(0, 5).map(po => `
                                <tr>
                                    <td>${po.id}</td>
                                    <td>$${po.amount.toLocaleString()}</td>
                                    <td><span class="status-badge status-${po.status}">${PO_STATUSES[po.status]}</span></td>
                                    <td>${formatDate(po.created)}</td>
                                    <td>
                                        <button class="action-btn action-view" onclick="closeModal(); viewPO('${po.id}')">View</button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                    ${customerPOs.length > 5 ? `<p><em>Showing 5 of ${customerPOs.length} orders</em></p>` : ''}
                </div>
            ` : '<p><em>No purchase orders found for this customer.</em></p>'}
        </div>
        
        <div class="form-actions">
            <button type="button" class="btn btn-secondary" onclick="closeModal()">Close</button>
            ${auth.canWrite() ? `<button type="button" class="btn btn-warning" onclick="editCustomer(${customer.id})">Edit</button>` : ''}
            ${auth.canWrite() ? `<button type="button" class="btn btn-primary" onclick="createPOForCustomer(${customer.id})">Create PO</button>` : ''}
        </div>
    `;
    
    showModal();
}

// Edit customer
function editCustomer(customerId) {
    if (!auth.canWrite()) {
        showNotification('You do not have permission to edit customers.', 'error');
        return;
    }
    
    const customer = getCustomerById(customerId);
    if (!customer) {
        showNotification('Customer not found.', 'error');
        return;
    }
    
    const modalBody = document.getElementById('modalBody');
    
    modalBody.innerHTML = `
        <h2>Edit Customer</h2>
        <form id="editCustomerForm" onsubmit="updateCustomer(event, ${customerId})">
            <div class="form-row">
                <div class="form-group">
                    <label for="editCustomerName">Customer Name:</label>
                    <input type="text" id="editCustomerName" required value="${customer.name}">
                </div>
                <div class="form-group">
                    <label for="editCustomerEmail">Email:</label>
                    <input type="email" id="editCustomerEmail" required value="${customer.email}">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="editCustomerPhone">Phone:</label>
                    <input type="tel" id="editCustomerPhone" required value="${customer.phone}">
                </div>
                <div class="form-group">
                    <label for="editCustomerType">Customer Type:</label>
                    <select id="editCustomerType" required onchange="toggleEditRenewalOptions()">
                        <option value="NN" ${customer.type === 'NN' ? 'selected' : ''}>NN - New Normal</option>
                        <option value="EN" ${customer.type === 'EN' ? 'selected' : ''}>EN - Existing Normal</option>
                        <option value="EE" ${customer.type === 'EE' ? 'selected' : ''}>EE - Existing Enterprise</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label for="editCustomerActive">Status:</label>
                <select id="editCustomerActive">
                    <option value="true" ${customer.active ? 'selected' : ''}>Active</option>
                    <option value="false" ${!customer.active ? 'selected' : ''}>Inactive</option>
                </select>
            </div>
            
            <div id="editRenewalOptions" class="renewal-options ${customer.type === 'EE' ? 'show' : ''}">
                <label>Renewal Types (for EE customers):</label>
                <div class="renewal-option">
                    <input type="checkbox" id="editRenewalLicense" value="license" 
                           ${customer.renewalTypes.includes('license') ? 'checked' : ''}>
                    <label for="editRenewalLicense">License Renewal</label>
                </div>
                <div class="renewal-option">
                    <input type="checkbox" id="editRenewalSupport" value="support_activity"
                           ${customer.renewalTypes.includes('support_activity') ? 'checked' : ''}>
                    <label for="editRenewalSupport">Support Activity Renewal</label>
                </div>
                <div class="renewal-option">
                    <input type="checkbox" id="editRenewalCR" value="cr"
                           ${customer.renewalTypes.includes('cr') ? 'checked' : ''}>
                    <label for="editRenewalCR">Change Request (CR) Renewal</label>
                </div>
            </div>
            
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                <button type="submit" class="btn btn-primary">Update Customer</button>
            </div>
        </form>
    `;
    
    showModal();
}

// Toggle renewal options for edit form
function toggleEditRenewalOptions() {
    const customerType = document.getElementById('editCustomerType').value;
    const renewalOptions = document.getElementById('editRenewalOptions');
    
    if (customerType === 'EE') {
        renewalOptions.classList.add('show');
    } else {
        renewalOptions.classList.remove('show');
        // Clear all checkboxes
        const checkboxes = renewalOptions.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(cb => cb.checked = false);
    }
}

// Update customer
function updateCustomer(event, customerId) {
    event.preventDefault();
    
    const name = document.getElementById('editCustomerName').value;
    const email = document.getElementById('editCustomerEmail').value;
    const phone = document.getElementById('editCustomerPhone').value;
    const type = document.getElementById('editCustomerType').value;
    const active = document.getElementById('editCustomerActive').value === 'true';
    
    // Collect renewal types for EE customers
    let renewalTypes = [];
    if (type === 'EE') {
        const checkboxes = document.querySelectorAll('#editRenewalOptions input[type="checkbox"]:checked');
        renewalTypes = Array.from(checkboxes).map(cb => cb.value);
    }
    
    const updates = {
        name,
        email,
        phone,
        type,
        active,
        renewalTypes
    };
    
    const updatedCustomer = updateCustomer(customerId, updates);
    
    if (updatedCustomer) {
        saveToLocalStorage();
        closeModal();
        showNotification('Customer updated successfully!', 'success');
        
        // Refresh the current view
        if (currentSection === 'customers') {
            loadCustomers();
        }
        loadDashboard(); // Update dashboard stats
    } else {
        showNotification('Error updating customer.', 'error');
    }
}

// Create PO for specific customer
function createPOForCustomer(customerId) {
    closeModal(); // Close customer details modal
    
    // Wait a bit for modal to close, then open PO creation
    setTimeout(() => {
        createNewPO();
        
        // Pre-select the customer
        setTimeout(() => {
            const customerSelect = document.getElementById('poCustomer');
            if (customerSelect) {
                customerSelect.value = customerId;
                updateCustomerInfo();
            }
        }, 100);
    }, 300);
}

// Filter customers by type
function filterCustomersByType(type) {
    const customers = getCustomers();
    const filteredCustomers = type ? customers.filter(c => c.type === type) : customers;
    
    const container = document.getElementById('customerList');
    if (!container) return;
    
    container.innerHTML = `
        <div class="customer-list">
            ${filteredCustomers.map(customer => `
                <div class="customer-item">
                    <div>${customer.name}</div>
                    <div>${customer.email}</div>
                    <div class="customer-type-badge customer-type-${customer.type.toLowerCase()}">${customer.type}</div>
                    <div>${customer.renewalTypes.length > 0 ? customer.renewalTypes.map(type => RENEWAL_TYPES[type]).join(', ') : 'N/A'}</div>
                    <div>
                        <button class="action-btn action-view" onclick="viewCustomer(${customer.id})">View</button>
                        ${auth.canWrite() ? `<button class="action-btn action-edit" onclick="editCustomer(${customer.id})">Edit</button>` : ''}
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

// Add click handlers for customer type cards
document.addEventListener('DOMContentLoaded', function() {
    const customerTypeCards = document.querySelectorAll('.customer-type-card');
    customerTypeCards.forEach(card => {
        card.addEventListener('click', function() {
            const type = this.getAttribute('data-type');
            filterCustomersByType(type);
            
            // Update active state
            customerTypeCards.forEach(c => c.classList.remove('active'));
            this.classList.add('active');
        });
    });
});
