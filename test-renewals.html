<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Renewals</title>
</head>
<body>
    <h1>Testing Renewal Functions</h1>
    <div id="output"></div>

    <script src="js/data.js"></script>
    <script>
        // Test renewal functions
        console.log('Testing renewal functions...');
        
        try {
            // Test data access
            const renewals = getRenewals();
            console.log('Renewals:', renewals);
            
            // Test stats
            const renewalStats = getRenewalStats();
            console.log('Renewal Stats:', renewalStats);
            
            // Test timeline stats
            const timelineStats = getRenewalTimelineStats();
            console.log('Timeline Stats:', timelineStats);
            
            // Display results
            document.getElementById('output').innerHTML = `
                <h2>Test Results</h2>
                <p>Total Renewals: ${renewals.length}</p>
                <p>License Received: $${renewalStats.license.received}</p>
                <p>License Pending: $${renewalStats.license.pending}</p>
                <p>Support Received: $${renewalStats.support_activity.received}</p>
                <p>Support Pending: $${renewalStats.support_activity.pending}</p>
                <p>CR Received: $${renewalStats.cr.received}</p>
                <p>CR Pending: $${renewalStats.cr.pending}</p>
                <p>Today Revenue: $${timelineStats.today}</p>
                <p>Weekly Revenue: $${timelineStats.weekly}</p>
                <p>MTD Revenue: $${timelineStats.mtd}</p>
                <p>YTD Revenue: $${timelineStats.ytd}</p>
            `;
            
        } catch (error) {
            console.error('Error:', error);
            document.getElementById('output').innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
        }
    </script>
</body>
</html>
