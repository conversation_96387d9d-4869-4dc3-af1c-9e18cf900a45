<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HCL Software Portal - Login</title>
    <link rel="stylesheet" href="styles/main.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #0066cc 0%, #004499 50%, #00a86b 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
            position: relative;
            overflow: hidden;
        }

        /* Animated Background */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(1deg); }
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 450px;
            text-align: center;
            position: relative;
            z-index: 1;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .login-header {
            margin-bottom: 2.5rem;
        }

        .company-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .company-logo i {
            font-size: 3rem;
            background: linear-gradient(135deg, #0066cc, #00a86b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .login-header h1 {
            color: #1e293b;
            margin-bottom: 0.5rem;
            font-size: 2.2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #0066cc, #004499);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .login-header p {
            color: #64748b;
            margin: 0;
            font-size: 1rem;
            font-weight: 500;
        }

        .login-form {
            text-align: left;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-group label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
            font-weight: 600;
            color: #374151;
            font-size: 0.9rem;
        }

        .form-group input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-sizing: border-box;
            background: rgba(255, 255, 255, 0.8);
        }

        .form-group input:focus {
            outline: none;
            border-color: #0066cc;
            box-shadow: 0 0 0 4px rgba(0, 102, 204, 0.1);
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
        }

        .login-btn {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(135deg, #0066cc 0%, #004499 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            position: relative;
            overflow: hidden;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 102, 204, 0.3);
        }

        .login-btn:active {
            transform: translateY(-1px);
        }

        .demo-credentials {
            margin-top: 2rem;
            padding: 2rem;
            background: rgba(248, 250, 252, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            border: 1px solid rgba(0, 102, 204, 0.2);
            position: relative;
        }

        .demo-credentials::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(135deg, #0066cc, #00a86b);
            border-radius: 2px;
        }

        .demo-credentials h3 {
            margin-top: 0;
            margin-bottom: 1.5rem;
            color: #1e293b;
            font-size: 1.2rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .credential-item {
            margin-bottom: 1rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 102, 204, 0.1);
            position: relative;
            overflow: hidden;
        }

        .credential-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, #0066cc, #00a86b);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .credential-item:hover::before {
            transform: scaleX(1);
        }

        .credential-item:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 102, 204, 0.15);
        }

        .credential-item strong {
            color: #0066cc;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .credential-item small {
            color: #64748b;
            display: block;
            margin-top: 0.5rem;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .platform-features {
            margin-top: 2rem;
            text-align: center;
        }

        .platform-features h3 {
            color: #1e293b;
            margin-bottom: 1.5rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin-top: 1rem;
        }

        .feature-item {
            padding: 1.5rem;
            background: rgba(0, 102, 204, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 102, 204, 0.2);
            position: relative;
            overflow: hidden;
        }

        .feature-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 102, 204, 0.1), rgba(0, 168, 107, 0.1));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .feature-item:hover::before {
            opacity: 1;
        }

        .feature-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 102, 204, 0.2);
        }

        .feature-item i {
            font-size: 2rem;
            color: #0066cc;
            margin-bottom: 1rem;
            position: relative;
            z-index: 1;
        }

        .feature-item h4 {
            margin: 0 0 0.5rem 0;
            color: #1e293b;
            font-size: 1rem;
            font-weight: 600;
            position: relative;
            z-index: 1;
        }

        .feature-item p {
            margin: 0;
            font-size: 0.9rem;
            color: #64748b;
            position: relative;
            z-index: 1;
        }

        .error-message {
            background: linear-gradient(135deg, #fee2e2, #fecaca);
            color: #991b1b;
            padding: 1rem;
            border-radius: 12px;
            margin-bottom: 1rem;
            display: none;
            border: 1px solid #fca5a5;
            font-weight: 500;
        }

        .success-message {
            background: linear-gradient(135deg, #d1fae5, #a7f3d0);
            color: #065f46;
            padding: 1rem;
            border-radius: 12px;
            margin-bottom: 1rem;
            display: none;
            border: 1px solid #6ee7b7;
            font-weight: 500;
        }

        /* Loading Animation */
        .loading {
            display: none;
        }

        .loading.show {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #ffffff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .login-container {
                margin: 1rem;
                padding: 2rem;
                max-width: 90%;
            }

            .company-logo i {
                font-size: 2.5rem;
            }

            .login-header h1 {
                font-size: 1.8rem;
            }

            .feature-grid {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }

            .feature-item {
                padding: 1rem;
            }

            .demo-credentials {
                padding: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .login-container {
                padding: 1.5rem;
                margin: 0.5rem;
            }

            .company-logo {
                flex-direction: column;
                gap: 0.5rem;
            }

            .login-header h1 {
                font-size: 1.5rem;
            }

            .form-group input {
                padding: 0.875rem;
            }

            .login-btn {
                padding: 0.875rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="company-logo">
                <i class="fas fa-cube"></i>
                <div>
                    <h1>HCL Software</h1>
                    <p>Enterprise Portal Solution</p>
                </div>
            </div>
        </div>

        <div id="errorMessage" class="error-message"></div>
        <div id="successMessage" class="success-message"></div>

        <form class="login-form" onsubmit="handleLogin(event)">
            <div class="form-group">
                <label for="email">
                    <i class="fas fa-envelope"></i>
                    Email Address
                </label>
                <input type="email" id="email" name="email" required placeholder="Enter your email address">
            </div>

            <div class="form-group">
                <label for="password">
                    <i class="fas fa-lock"></i>
                    Password
                </label>
                <input type="password" id="password" name="password" required placeholder="Enter your password">
            </div>

            <button type="submit" class="login-btn">
                <i class="fas fa-sign-in-alt"></i>
                <span>Sign In to Portal</span>
                <div class="loading" id="loginLoading"></div>
            </button>
        </form>

        <div class="demo-credentials">
            <h3>
                <i class="fas fa-key"></i>
                Demo Credentials
            </h3>
            <div class="credential-item" onclick="fillCredentials('<EMAIL>', 'admin123')">
                <strong>
                    <i class="fas fa-crown"></i>
                    Admin User
                </strong>
                <small>Full system access including user management, settings, and all administrative functions</small>
            </div>
            <div class="credential-item" onclick="fillCredentials('<EMAIL>', 'jane123')">
                <strong>
                    <i class="fas fa-user-edit"></i>
                    Manager User
                </strong>
                <small>Read & Write permissions with approval workflow for purchase orders and customer management</small>
            </div>
            <div class="credential-item" onclick="fillCredentials('<EMAIL>', 'bob123')">
                <strong>
                    <i class="fas fa-eye"></i>
                    Viewer User
                </strong>
                <small>Read-only access to view dashboards, reports, and system data without edit permissions</small>
            </div>
        </div>

        <div class="platform-features">
            <h3>
                <i class="fas fa-rocket"></i>
                Platform Features
            </h3>
            <div class="feature-grid">
                <div class="feature-item">
                    <i class="fas fa-shopping-cart"></i>
                    <h4>Purchase Orders</h4>
                    <p>Complete workflow management with approval processes</p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-users"></i>
                    <h4>Customer Management</h4>
                    <p>NN, EN, EE types with renewal tracking</p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-user-shield"></i>
                    <h4>Role-Based Access</h4>
                    <p>Admin, Read-Only, and Read-Write roles</p>
                </div>
            </div>
        </div>
    </div>

    <script src="js/data.js"></script>
    <script>
        // Fill credentials when demo user is clicked
        function fillCredentials(email, password) {
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;
        }

        // Enhanced login form submission
        function handleLogin(event) {
            event.preventDefault();

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const loginBtn = event.target.querySelector('.login-btn');
            const loading = document.getElementById('loginLoading');

            // Clear previous messages
            hideMessages();

            // Show loading state
            loginBtn.disabled = true;
            loading.classList.add('show');
            loginBtn.querySelector('span').textContent = 'Signing In...';

            // Simulate API call delay
            setTimeout(() => {
                // Mock authentication (in real app, this would be an API call)
                const result = authenticateUser(email, password);

                if (result.success) {
                    showSuccess('Login successful! Redirecting to portal...');

                    // Save user session
                    const session = {
                        user: result.user,
                        timestamp: Date.now()
                    };
                    localStorage.setItem('companyPortalSession', JSON.stringify(session));

                    // Redirect to main portal
                    setTimeout(() => {
                        window.location.href = 'Company.html';
                    }, 1500);
                } else {
                    showError(result.message);

                    // Reset button state
                    loginBtn.disabled = false;
                    loading.classList.remove('show');
                    loginBtn.querySelector('span').textContent = 'Sign In to Portal';
                }
            }, 1000); // Simulate network delay
        }

        // Mock authentication function
        function authenticateUser(email, password) {
            // Load users from data
            const users = mockData.users;
            const user = users.find(u => u.email === email && u.active);
            
            if (user) {
                // Mock password validation
                const validCredentials = {
                    '<EMAIL>': 'admin123',
                    '<EMAIL>': 'jane123',
                    '<EMAIL>': 'bob123'
                };
                
                if (validCredentials[email] === password) {
                    return { success: true, user: user };
                }
            }
            
            return { success: false, message: 'Invalid email or password' };
        }

        // Show error message
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        // Show success message
        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
        }

        // Hide all messages
        function hideMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
        }

        // Check if user is already logged in
        document.addEventListener('DOMContentLoaded', function() {
            const session = localStorage.getItem('companyPortalSession');
            if (session) {
                try {
                    const parsed = JSON.parse(session);
                    const now = Date.now();
                    const sessionTimeout = 30 * 60 * 1000; // 30 minutes
                    
                    // Check if session is still valid
                    if (now - parsed.timestamp < sessionTimeout) {
                        // Redirect to main portal
                        window.location.href = 'Company.html';
                        return;
                    }
                } catch (e) {
                    // Invalid session, continue with login
                }
            }
        });
    </script>
</body>
</html>
