// Renewal management functionality

let currentRenewalPage = 1;
const renewalsPerPage = 10;
let filteredRenewals = [];

// Load renewals section
function loadRenewals() {
    loadRenewalStats();
    loadRenewalTimeline();
    loadRenewalFilters();
    loadRenewalTable();
}

// Load renewal statistics
function loadRenewalStats() {
    const stats = getRenewalStats();
    
    // Update license stats
    document.getElementById('licenseReceived').textContent = `$${(stats.license.received / 1000).toFixed(0)}K`;
    document.getElementById('licensePending').textContent = `$${(stats.license.pending / 1000).toFixed(0)}K`;
    
    // Update support stats
    document.getElementById('supportReceived').textContent = `$${(stats.support_activity.received / 1000).toFixed(0)}K`;
    document.getElementById('supportPending').textContent = `$${(stats.support_activity.pending / 1000).toFixed(0)}K`;
    
    // Update CR stats
    document.getElementById('crReceived').textContent = `$${(stats.cr.received / 1000).toFixed(0)}K`;
    document.getElementById('crPending').textContent = `$${(stats.cr.pending / 1000).toFixed(0)}K`;
}

// Load renewal timeline
function loadRenewalTimeline() {
    const timelineStats = getRenewalTimelineStats();
    
    // Update timeline numbers
    document.getElementById('todayRenewals').textContent = `$${(timelineStats.today / 1000).toFixed(0)}K`;
    document.getElementById('weeklyRenewals').textContent = `$${(timelineStats.weekly / 1000).toFixed(0)}K`;
    document.getElementById('mtdRenewals').textContent = `$${(timelineStats.mtd / 1000).toFixed(0)}K`;
    document.getElementById('ytdRenewals').textContent = `$${(timelineStats.ytd / 1000).toFixed(0)}K`;
    
    // Calculate and update progress bars
    const targets = {
        today: 25000,
        weekly: 125000,
        mtd: 500000,
        ytd: 6000000
    };
    
    Object.keys(targets).forEach(period => {
        const percentage = Math.min((timelineStats[period] / targets[period]) * 100, 100);
        const progressBar = document.getElementById(`${period}RenewalProgressBar`);
        const percentageSpan = document.getElementById(`${period}RenewalPercentage`);
        
        if (progressBar && percentageSpan) {
            progressBar.style.width = `${percentage}%`;
            percentageSpan.textContent = `${percentage.toFixed(1)}%`;
        }
    });
}

// Load renewal filters
function loadRenewalFilters() {
    const customerFilter = document.getElementById('renewalCustomerFilter');
    const customers = getCustomers().filter(c => c.type === 'EE');
    
    customerFilter.innerHTML = '<option value="">All Customers</option>';
    customers.forEach(customer => {
        customerFilter.innerHTML += `<option value="${customer.id}">${customer.name}</option>`;
    });
}

// Load renewal table
function loadRenewalTable() {
    const renewals = getRenewals();
    filteredRenewals = renewals;
    
    // Apply current filters
    filterRenewals();
}

// Filter renewals
function filterRenewals() {
    const searchTerm = document.getElementById('renewalSearch').value.toLowerCase();
    const typeFilter = document.getElementById('renewalTypeFilter').value;
    const statusFilter = document.getElementById('renewalStatusFilter').value;
    const customerFilter = document.getElementById('renewalCustomerFilter').value;
    
    let renewals = getRenewals();
    
    // Apply filters
    if (searchTerm) {
        renewals = renewals.filter(renewal => 
            renewal.id.toLowerCase().includes(searchTerm) ||
            renewal.customerName.toLowerCase().includes(searchTerm) ||
            renewal.description.toLowerCase().includes(searchTerm)
        );
    }
    
    if (typeFilter) {
        renewals = renewals.filter(renewal => renewal.renewalType === typeFilter);
    }
    
    if (statusFilter) {
        renewals = renewals.filter(renewal => renewal.status === statusFilter);
    }
    
    if (customerFilter) {
        renewals = renewals.filter(renewal => renewal.customerId === parseInt(customerFilter));
    }
    
    filteredRenewals = renewals;
    currentRenewalPage = 1;
    displayRenewalTable();
}

// Display renewal table
function displayRenewalTable() {
    const startIndex = (currentRenewalPage - 1) * renewalsPerPage;
    const endIndex = startIndex + renewalsPerPage;
    const pageRenewals = filteredRenewals.slice(startIndex, endIndex);
    
    const tableBody = document.getElementById('renewalTableBody');
    
    if (pageRenewals.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="7" style="text-align: center; padding: 2rem; color: var(--gray-500);">
                    <i class="fas fa-inbox" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                    No renewals found matching your criteria.
                </td>
            </tr>
        `;
    } else {
        tableBody.innerHTML = pageRenewals.map(renewal => `
            <tr>
                <td>
                    <div class="renewal-id">
                        <strong>${renewal.id}</strong>
                    </div>
                </td>
                <td>
                    <div class="customer-info">
                        <strong>${renewal.customerName}</strong>
                        <div class="customer-type-badge customer-type-${renewal.customerType.toLowerCase()}">${renewal.customerType}</div>
                    </div>
                </td>
                <td>
                    <div class="renewal-type">
                        <i class="fas ${getRenewalTypeIcon(renewal.renewalType)}"></i>
                        ${RENEWAL_TYPES[renewal.renewalType]}
                    </div>
                </td>
                <td>
                    <div class="amount">
                        <strong>$${renewal.amount.toLocaleString()}</strong>
                    </div>
                </td>
                <td>
                    <div class="renewal-date">
                        ${formatDate(renewal.renewalDate)}
                        ${isOverdue(renewal.renewalDate, renewal.status) ? '<span class="overdue-indicator">⚠️</span>' : ''}
                    </div>
                </td>
                <td>
                    <span class="status-badge status-${renewal.status}">
                        ${getStatusIcon(renewal.status)} ${RENEWAL_STATUSES[renewal.status]}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn action-view" onclick="viewRenewal('${renewal.id}')" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${auth.canWrite() ? `
                            <button class="action-btn action-edit" onclick="editRenewal('${renewal.id}')" title="Edit">
                                <i class="fas fa-edit"></i>
                            </button>
                        ` : ''}
                        ${renewal.status === 'pending' && auth.canWrite() ? `
                            <button class="action-btn action-approve" onclick="markAsReceived('${renewal.id}')" title="Mark as Received">
                                <i class="fas fa-check"></i>
                            </button>
                        ` : ''}
                        ${auth.canWrite() ? `
                            <button class="action-btn action-notify" onclick="sendRenewalNotification('${renewal.id}')" title="Send Notification">
                                <i class="fas fa-bell"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    // Update table info and pagination
    updateRenewalTableInfo();
    updateRenewalPagination();
}

// Helper functions
function getRenewalTypeIcon(type) {
    const icons = {
        license: 'fa-key',
        support_activity: 'fa-headset',
        cr: 'fa-edit'
    };
    return icons[type] || 'fa-sync-alt';
}

function getStatusIcon(status) {
    const icons = {
        pending: '⏳',
        received: '✅',
        overdue: '❌',
        cancelled: '🚫'
    };
    return icons[status] || '❓';
}

function isOverdue(renewalDate, status) {
    if (status !== 'pending') return false;
    const today = new Date().toISOString().split('T')[0];
    return renewalDate < today;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// Update table info
function updateRenewalTableInfo() {
    const tableInfo = document.getElementById('renewalTableInfo');
    const startIndex = (currentRenewalPage - 1) * renewalsPerPage + 1;
    const endIndex = Math.min(currentRenewalPage * renewalsPerPage, filteredRenewals.length);
    
    if (filteredRenewals.length === 0) {
        tableInfo.textContent = 'Showing 0 of 0 renewals';
    } else {
        tableInfo.textContent = `Showing ${startIndex}-${endIndex} of ${filteredRenewals.length} renewals`;
    }
}

// Update pagination
function updateRenewalPagination() {
    const totalPages = Math.ceil(filteredRenewals.length / renewalsPerPage);
    const pageInfo = document.getElementById('renewalPageInfo');
    const prevBtn = document.getElementById('prevRenewalBtn');
    const nextBtn = document.getElementById('nextRenewalBtn');
    
    pageInfo.textContent = `Page ${currentRenewalPage} of ${totalPages}`;
    
    prevBtn.disabled = currentRenewalPage <= 1;
    nextBtn.disabled = currentRenewalPage >= totalPages;
}

// Pagination functions
function previousRenewalPage() {
    if (currentRenewalPage > 1) {
        currentRenewalPage--;
        displayRenewalTable();
    }
}

function nextRenewalPage() {
    const totalPages = Math.ceil(filteredRenewals.length / renewalsPerPage);
    if (currentRenewalPage < totalPages) {
        currentRenewalPage++;
        displayRenewalTable();
    }
}

// Search renewals
function searchRenewals() {
    filterRenewals();
}

// Clear filters
function clearRenewalFilters() {
    document.getElementById('renewalSearch').value = '';
    document.getElementById('renewalTypeFilter').value = '';
    document.getElementById('renewalStatusFilter').value = '';
    document.getElementById('renewalCustomerFilter').value = '';
    filterRenewals();
}

// Filter by renewal type
function filterRenewalsByType(type) {
    document.getElementById('renewalTypeFilter').value = type;
    filterRenewals();
}

// Create new renewal
function createNewRenewal() {
    if (!auth.canWrite()) {
        showNotification('You do not have permission to create renewals.', 'error');
        return;
    }

    const customers = getCustomers().filter(c => c.type === 'EE');
    const modalBody = document.getElementById('modalBody');

    modalBody.innerHTML = `
        <h2>Create New Renewal</h2>
        <form id="renewalForm" onsubmit="saveRenewal(event)">
            <div class="form-row">
                <div class="form-group">
                    <label for="renewalCustomer">Customer:</label>
                    <select id="renewalCustomer" required onchange="updateRenewalCustomerInfo()">
                        <option value="">Select Customer</option>
                        ${customers.map(c => `<option value="${c.id}" data-type="${c.type}">${c.name} (${c.type})</option>`).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label for="renewalType">Renewal Type:</label>
                    <select id="renewalType" required>
                        <option value="">Select Type</option>
                        <option value="license">🔑 License Renewal</option>
                        <option value="support_activity">🎧 Support Activity</option>
                        <option value="cr">📝 Change Request</option>
                    </select>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="renewalAmount">Amount ($):</label>
                    <input type="number" id="renewalAmount" min="0" step="100" required placeholder="Enter amount">
                </div>
                <div class="form-group">
                    <label for="renewalDate">Renewal Date:</label>
                    <input type="date" id="renewalDate" required>
                </div>
            </div>

            <div class="form-group">
                <label for="renewalDescription">Description:</label>
                <textarea id="renewalDescription" placeholder="Enter renewal description" required></textarea>
            </div>

            <div id="renewalCustomerInfo" style="display: none; margin: 1rem 0; padding: 1rem; background: #f8f9fa; border-radius: 5px;">
                <h4>Customer Information</h4>
                <div id="renewalCustomerDetails"></div>
            </div>

            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                <button type="submit" class="btn btn-primary">Create Renewal</button>
            </div>
        </form>
    `;

    // Set default renewal date to 30 days from now
    const defaultDate = new Date();
    defaultDate.setDate(defaultDate.getDate() + 30);
    document.getElementById('renewalDate').value = defaultDate.toISOString().split('T')[0];

    showModal();
}

// Update customer info in renewal form
function updateRenewalCustomerInfo() {
    const customerSelect = document.getElementById('renewalCustomer');
    const customerInfo = document.getElementById('renewalCustomerInfo');
    const customerDetails = document.getElementById('renewalCustomerDetails');

    if (customerSelect.value) {
        const customer = getCustomerById(parseInt(customerSelect.value));
        if (customer) {
            customerDetails.innerHTML = `
                <p><strong>Type:</strong> ${CUSTOMER_TYPES[customer.type].name}</p>
                <p><strong>Email:</strong> ${customer.email}</p>
                <p><strong>Phone:</strong> ${customer.phone}</p>
                <p><strong>Available Renewal Types:</strong> ${customer.renewalTypes.map(type => RENEWAL_TYPES[type]).join(', ')}</p>
            `;
            customerInfo.style.display = 'block';
        }
    } else {
        customerInfo.style.display = 'none';
    }
}

// Save renewal
function saveRenewal(event) {
    event.preventDefault();

    const customerId = parseInt(document.getElementById('renewalCustomer').value);
    const renewalType = document.getElementById('renewalType').value;
    const amount = parseFloat(document.getElementById('renewalAmount').value);
    const renewalDate = document.getElementById('renewalDate').value;
    const description = document.getElementById('renewalDescription').value;

    const customer = getCustomerById(customerId);
    if (!customer) {
        showNotification('Invalid customer selected.', 'error');
        return;
    }

    // Validate renewal type for customer
    if (!customer.renewalTypes.includes(renewalType)) {
        showNotification(`${customer.name} does not support ${RENEWAL_TYPES[renewalType]}.`, 'error');
        return;
    }

    const newRenewal = {
        customerId: customerId,
        customerName: customer.name,
        customerType: customer.type,
        renewalType: renewalType,
        amount: amount,
        renewalDate: renewalDate,
        description: description,
        status: 'pending',
        paymentDate: null
    };

    const savedRenewal = addRenewal(newRenewal);
    saveToLocalStorage();

    closeModal();
    showNotification('Renewal created successfully!', 'success');

    // Refresh the current view
    if (currentSection === 'renewals') {
        loadRenewals();
    } else if (currentSection === 'dashboard') {
        loadDashboard();
    }
}

// View renewal details
function viewRenewal(renewalId) {
    const renewal = getRenewalById(renewalId);
    if (!renewal) {
        showNotification('Renewal not found.', 'error');
        return;
    }

    const customer = getCustomerById(renewal.customerId);
    const createdBy = getUserById(renewal.createdBy);

    const modalBody = document.getElementById('modalBody');
    modalBody.innerHTML = `
        <h2>Renewal Details</h2>
        <div class="renewal-details">
            <div class="detail-section">
                <h3><i class="fas fa-info-circle"></i> Basic Information</h3>
                <div class="detail-grid">
                    <div class="detail-item">
                        <label>Renewal ID:</label>
                        <span>${renewal.id}</span>
                    </div>
                    <div class="detail-item">
                        <label>Type:</label>
                        <span><i class="fas ${getRenewalTypeIcon(renewal.renewalType)}"></i> ${RENEWAL_TYPES[renewal.renewalType]}</span>
                    </div>
                    <div class="detail-item">
                        <label>Amount:</label>
                        <span class="amount-display">$${renewal.amount.toLocaleString()}</span>
                    </div>
                    <div class="detail-item">
                        <label>Status:</label>
                        <span class="status-badge status-${renewal.status}">
                            ${getStatusIcon(renewal.status)} ${RENEWAL_STATUSES[renewal.status]}
                        </span>
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <h3><i class="fas fa-calendar"></i> Dates</h3>
                <div class="detail-grid">
                    <div class="detail-item">
                        <label>Renewal Date:</label>
                        <span>${formatDate(renewal.renewalDate)}</span>
                    </div>
                    <div class="detail-item">
                        <label>Created Date:</label>
                        <span>${formatDate(renewal.created)}</span>
                    </div>
                    <div class="detail-item">
                        <label>Payment Date:</label>
                        <span>${renewal.paymentDate ? formatDate(renewal.paymentDate) : 'Not paid'}</span>
                    </div>
                    <div class="detail-item">
                        <label>Days Until Due:</label>
                        <span class="${getDaysUntilDue(renewal.renewalDate) < 0 ? 'overdue' : ''}">${getDaysUntilDue(renewal.renewalDate)} days</span>
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <h3><i class="fas fa-building"></i> Customer Information</h3>
                <div class="detail-grid">
                    <div class="detail-item">
                        <label>Customer:</label>
                        <span>${renewal.customerName}</span>
                    </div>
                    <div class="detail-item">
                        <label>Type:</label>
                        <span class="customer-type-badge customer-type-${renewal.customerType.toLowerCase()}">${renewal.customerType}</span>
                    </div>
                    <div class="detail-item">
                        <label>Email:</label>
                        <span>${customer ? customer.email : 'N/A'}</span>
                    </div>
                    <div class="detail-item">
                        <label>Phone:</label>
                        <span>${customer ? customer.phone : 'N/A'}</span>
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <h3><i class="fas fa-file-alt"></i> Description</h3>
                <p class="description-text">${renewal.description}</p>
            </div>

            <div class="detail-section">
                <h3><i class="fas fa-user"></i> Created By</h3>
                <p>${createdBy ? createdBy.name : 'Unknown'} on ${formatDate(renewal.created)}</p>
            </div>
        </div>

        <div class="modal-actions">
            ${auth.canWrite() && renewal.status === 'pending' ? `
                <button class="btn btn-success" onclick="markAsReceived('${renewal.id}')">
                    <i class="fas fa-check"></i> Mark as Received
                </button>
                <button class="btn btn-warning" onclick="editRenewal('${renewal.id}')">
                    <i class="fas fa-edit"></i> Edit Renewal
                </button>
            ` : ''}
            <button class="btn btn-secondary" onclick="closeModal()">Close</button>
        </div>
    `;

    showModal();
}

// Helper function to calculate days until due
function getDaysUntilDue(renewalDate) {
    const today = new Date();
    const due = new Date(renewalDate);
    const diffTime = due - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
}

// Edit renewal
function editRenewal(renewalId) {
    if (!auth.canWrite()) {
        showNotification('You do not have permission to edit renewals.', 'error');
        return;
    }

    const renewal = getRenewalById(renewalId);
    if (!renewal) {
        showNotification('Renewal not found.', 'error');
        return;
    }

    if (renewal.status === 'received') {
        showNotification('Cannot edit received renewals.', 'error');
        return;
    }

    const customers = getCustomers().filter(c => c.type === 'EE');
    const modalBody = document.getElementById('modalBody');

    modalBody.innerHTML = `
        <h2>Edit Renewal</h2>
        <form id="editRenewalForm" onsubmit="updateRenewalForm(event, '${renewalId}')">
            <div class="form-row">
                <div class="form-group">
                    <label for="editRenewalCustomer">Customer:</label>
                    <select id="editRenewalCustomer" required onchange="updateEditRenewalCustomerInfo()">
                        <option value="">Select Customer</option>
                        ${customers.map(c => `<option value="${c.id}" ${c.id === renewal.customerId ? 'selected' : ''}>${c.name} (${c.type})</option>`).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label for="editRenewalType">Renewal Type:</label>
                    <select id="editRenewalType" required>
                        <option value="">Select Type</option>
                        <option value="license" ${renewal.renewalType === 'license' ? 'selected' : ''}>🔑 License Renewal</option>
                        <option value="support_activity" ${renewal.renewalType === 'support_activity' ? 'selected' : ''}>🎧 Support Activity</option>
                        <option value="cr" ${renewal.renewalType === 'cr' ? 'selected' : ''}>📝 Change Request</option>
                    </select>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="editRenewalAmount">Amount ($):</label>
                    <input type="number" id="editRenewalAmount" min="0" step="100" required value="${renewal.amount}">
                </div>
                <div class="form-group">
                    <label for="editRenewalDate">Renewal Date:</label>
                    <input type="date" id="editRenewalDate" required value="${renewal.renewalDate}">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="editRenewalStatus">Status:</label>
                    <select id="editRenewalStatus" required>
                        <option value="pending" ${renewal.status === 'pending' ? 'selected' : ''}>⏳ Pending</option>
                        <option value="received" ${renewal.status === 'received' ? 'selected' : ''}>✅ Received</option>
                        <option value="overdue" ${renewal.status === 'overdue' ? 'selected' : ''}>❌ Overdue</option>
                        <option value="cancelled" ${renewal.status === 'cancelled' ? 'selected' : ''}>🚫 Cancelled</option>
                    </select>
                </div>
                <div class="form-group" id="editPaymentDateGroup" style="${renewal.status === 'received' ? '' : 'display: none;'}">
                    <label for="editPaymentDate">Payment Date:</label>
                    <input type="date" id="editPaymentDate" value="${renewal.paymentDate || ''}">
                </div>
            </div>

            <div class="form-group">
                <label for="editRenewalDescription">Description:</label>
                <textarea id="editRenewalDescription" required>${renewal.description}</textarea>
            </div>

            <div id="editRenewalCustomerInfo" style="margin: 1rem 0; padding: 1rem; background: #f8f9fa; border-radius: 5px;">
                <h4>Customer Information</h4>
                <div id="editRenewalCustomerDetails"></div>
            </div>

            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                <button type="submit" class="btn btn-primary">Update Renewal</button>
            </div>
        </form>
    `;

    // Initialize customer info
    updateEditRenewalCustomerInfo();

    // Add event listener for status change
    document.getElementById('editRenewalStatus').addEventListener('change', function() {
        const paymentDateGroup = document.getElementById('editPaymentDateGroup');
        if (this.value === 'received') {
            paymentDateGroup.style.display = 'block';
            if (!document.getElementById('editPaymentDate').value) {
                document.getElementById('editPaymentDate').value = new Date().toISOString().split('T')[0];
            }
        } else {
            paymentDateGroup.style.display = 'none';
        }
    });

    showModal();
}

// Update customer info in edit renewal form
function updateEditRenewalCustomerInfo() {
    const customerSelect = document.getElementById('editRenewalCustomer');
    const customerDetails = document.getElementById('editRenewalCustomerDetails');

    if (customerSelect.value) {
        const customer = getCustomerById(parseInt(customerSelect.value));
        if (customer) {
            customerDetails.innerHTML = `
                <p><strong>Type:</strong> ${CUSTOMER_TYPES[customer.type].name}</p>
                <p><strong>Email:</strong> ${customer.email}</p>
                <p><strong>Phone:</strong> ${customer.phone}</p>
                <p><strong>Available Renewal Types:</strong> ${customer.renewalTypes.map(type => RENEWAL_TYPES[type]).join(', ')}</p>
            `;
        }
    }
}

// Update renewal
function updateRenewalForm(event, renewalId) {
    event.preventDefault();

    const customerId = parseInt(document.getElementById('editRenewalCustomer').value);
    const renewalType = document.getElementById('editRenewalType').value;
    const amount = parseFloat(document.getElementById('editRenewalAmount').value);
    const renewalDate = document.getElementById('editRenewalDate').value;
    const status = document.getElementById('editRenewalStatus').value;
    const paymentDate = document.getElementById('editPaymentDate').value || null;
    const description = document.getElementById('editRenewalDescription').value;

    const customer = getCustomerById(customerId);
    if (!customer) {
        showNotification('Invalid customer selected.', 'error');
        return;
    }

    // Validate renewal type for customer
    if (!customer.renewalTypes.includes(renewalType)) {
        showNotification(`${customer.name} does not support ${RENEWAL_TYPES[renewalType]}.`, 'error');
        return;
    }

    const updates = {
        customerId: customerId,
        customerName: customer.name,
        customerType: customer.type,
        renewalType: renewalType,
        amount: amount,
        renewalDate: renewalDate,
        status: status,
        paymentDate: paymentDate,
        description: description
    };

    const updatedRenewal = updateRenewal(renewalId, updates);
    if (updatedRenewal) {
        saveToLocalStorage();
        closeModal();
        showNotification('Renewal updated successfully!', 'success');

        // Refresh the current view
        if (currentSection === 'renewals') {
            loadRenewals();
        }
    } else {
        showNotification('Failed to update renewal.', 'error');
    }
}

// Mark renewal as received
function markAsReceived(renewalId) {
    if (!auth.canWrite()) {
        showNotification('You do not have permission to update renewals.', 'error');
        return;
    }

    const renewal = getRenewalById(renewalId);
    if (!renewal) {
        showNotification('Renewal not found.', 'error');
        return;
    }

    if (renewal.status === 'received') {
        showNotification('Renewal is already marked as received.', 'info');
        return;
    }

    const paymentDate = new Date().toISOString().split('T')[0];
    const updatedRenewal = updateRenewalStatus(renewalId, 'received', paymentDate);

    if (updatedRenewal) {
        saveToLocalStorage();
        showNotification('Renewal marked as received!', 'success');

        // Close modal if open
        closeModal();

        // Refresh the current view
        if (currentSection === 'renewals') {
            loadRenewals();
        } else if (currentSection === 'dashboard') {
            loadDashboard();
        }
    } else {
        showNotification('Failed to update renewal status.', 'error');
    }
}

// Send renewal notification
function sendRenewalNotification(renewalId) {
    const renewal = getRenewalById(renewalId);
    if (!renewal) {
        showNotification('Renewal not found.', 'error');
        return;
    }

    const customer = getCustomerById(renewal.customerId);
    if (!customer) {
        showNotification('Customer not found.', 'error');
        return;
    }

    const modalBody = document.getElementById('modalBody');
    modalBody.innerHTML = `
        <h2>Send Renewal Notification</h2>
        <div class="notification-setup">
            <div class="renewal-summary">
                <h3><i class="fas fa-info-circle"></i> Renewal Details</h3>
                <div class="summary-grid">
                    <div class="summary-item">
                        <label>Customer:</label>
                        <span>${renewal.customerName}</span>
                    </div>
                    <div class="summary-item">
                        <label>Type:</label>
                        <span><i class="fas ${getRenewalTypeIcon(renewal.renewalType)}"></i> ${RENEWAL_TYPES[renewal.renewalType]}</span>
                    </div>
                    <div class="summary-item">
                        <label>Amount:</label>
                        <span class="amount-display">$${renewal.amount.toLocaleString()}</span>
                    </div>
                    <div class="summary-item">
                        <label>Due Date:</label>
                        <span>${formatDate(renewal.renewalDate)}</span>
                    </div>
                    <div class="summary-item">
                        <label>Days Until Due:</label>
                        <span class="${getDaysUntilDue(renewal.renewalDate) < 0 ? 'overdue' : getDaysUntilDue(renewal.renewalDate) <= 7 ? 'urgent' : ''}">${getDaysUntilDue(renewal.renewalDate)} days</span>
                    </div>
                </div>
            </div>

            <form id="notificationForm" onsubmit="sendNotification(event, '${renewalId}')">
                <div class="form-group">
                    <label for="notificationType">Notification Type:</label>
                    <select id="notificationType" required onchange="updateNotificationTemplate()">
                        <option value="">Select notification type</option>
                        <option value="reminder">📅 Renewal Reminder</option>
                        <option value="urgent">⚠️ Urgent Renewal Notice</option>
                        <option value="overdue">❌ Overdue Payment Notice</option>
                        <option value="thank_you">✅ Payment Confirmation</option>
                        <option value="custom">📝 Custom Message</option>
                    </select>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="notificationMethod">Delivery Method:</label>
                        <select id="notificationMethod" required>
                            <option value="email">📧 Email</option>
                            <option value="sms">📱 SMS</option>
                            <option value="both">📧📱 Email & SMS</option>
                            <option value="portal">🔔 Portal Notification</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="notificationPriority">Priority:</label>
                        <select id="notificationPriority" required>
                            <option value="normal">🔵 Normal</option>
                            <option value="high">🟡 High</option>
                            <option value="urgent">🔴 Urgent</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="recipientEmail">Recipient Email:</label>
                    <input type="email" id="recipientEmail" value="${customer.email}" required>
                </div>

                <div class="form-group">
                    <label for="recipientPhone">Recipient Phone (for SMS):</label>
                    <input type="tel" id="recipientPhone" value="${customer.phone}" placeholder="******-0000">
                </div>

                <div class="form-group">
                    <label for="notificationSubject">Subject:</label>
                    <input type="text" id="notificationSubject" required placeholder="Notification subject">
                </div>

                <div class="form-group">
                    <label for="notificationMessage">Message:</label>
                    <textarea id="notificationMessage" rows="6" required placeholder="Notification message"></textarea>
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="scheduleNotification">
                        Schedule for later delivery
                    </label>
                </div>

                <div class="form-group" id="scheduleGroup" style="display: none;">
                    <label for="scheduleDateTime">Schedule Date & Time:</label>
                    <input type="datetime-local" id="scheduleDateTime">
                </div>

                <div class="notification-preview" id="notificationPreview" style="display: none;">
                    <h4><i class="fas fa-eye"></i> Preview</h4>
                    <div class="preview-content" id="previewContent"></div>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-outline" onclick="previewNotification()">
                        <i class="fas fa-eye"></i> Preview
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i> Send Notification
                    </button>
                </div>
            </form>
        </div>
    `;

    // Add event listener for schedule checkbox
    document.getElementById('scheduleNotification').addEventListener('change', function() {
        const scheduleGroup = document.getElementById('scheduleGroup');
        if (this.checked) {
            scheduleGroup.style.display = 'block';
            // Set default to 1 hour from now
            const defaultTime = new Date();
            defaultTime.setHours(defaultTime.getHours() + 1);
            document.getElementById('scheduleDateTime').value = defaultTime.toISOString().slice(0, 16);
        } else {
            scheduleGroup.style.display = 'none';
        }
    });

    // Set default notification type based on renewal status
    const daysUntilDue = getDaysUntilDue(renewal.renewalDate);
    if (renewal.status === 'overdue') {
        document.getElementById('notificationType').value = 'overdue';
    } else if (daysUntilDue <= 7) {
        document.getElementById('notificationType').value = 'urgent';
    } else {
        document.getElementById('notificationType').value = 'reminder';
    }

    updateNotificationTemplate();
    showModal();
}

// Update notification template based on type
function updateNotificationTemplate() {
    const notificationType = document.getElementById('notificationType').value;
    const subjectField = document.getElementById('notificationSubject');
    const messageField = document.getElementById('notificationMessage');

    if (!notificationType) return;

    const renewal = getRenewalById(document.querySelector('#notificationForm').getAttribute('onsubmit').match(/'([^']+)'/)[1]);
    const customer = getCustomerById(renewal.customerId);

    const templates = {
        reminder: {
            subject: `Renewal Reminder: ${RENEWAL_TYPES[renewal.renewalType]} Due ${formatDate(renewal.renewalDate)}`,
            message: `Dear ${customer.name},

This is a friendly reminder that your ${RENEWAL_TYPES[renewal.renewalType].toLowerCase()} is due for renewal.

Renewal Details:
- Type: ${RENEWAL_TYPES[renewal.renewalType]}
- Amount: $${renewal.amount.toLocaleString()}
- Due Date: ${formatDate(renewal.renewalDate)}
- Days Remaining: ${getDaysUntilDue(renewal.renewalDate)}

Description: ${renewal.description}

Please ensure timely payment to avoid any service interruptions.

Best regards,
HCL Software Team`
        },
        urgent: {
            subject: `URGENT: ${RENEWAL_TYPES[renewal.renewalType]} Renewal Due Soon`,
            message: `Dear ${customer.name},

URGENT NOTICE: Your ${RENEWAL_TYPES[renewal.renewalType].toLowerCase()} renewal is due very soon.

Renewal Details:
- Type: ${RENEWAL_TYPES[renewal.renewalType]}
- Amount: $${renewal.amount.toLocaleString()}
- Due Date: ${formatDate(renewal.renewalDate)}
- Days Remaining: ${getDaysUntilDue(renewal.renewalDate)}

IMMEDIATE ACTION REQUIRED to avoid service disruption.

Please contact us immediately to process your renewal.

Urgent regards,
HCL Software Team`
        },
        overdue: {
            subject: `OVERDUE: ${RENEWAL_TYPES[renewal.renewalType]} Payment Required`,
            message: `Dear ${customer.name},

OVERDUE NOTICE: Your ${RENEWAL_TYPES[renewal.renewalType].toLowerCase()} payment is now overdue.

Renewal Details:
- Type: ${RENEWAL_TYPES[renewal.renewalType]}
- Amount: $${renewal.amount.toLocaleString()}
- Due Date: ${formatDate(renewal.renewalDate)}
- Days Overdue: ${Math.abs(getDaysUntilDue(renewal.renewalDate))}

Your services may be suspended if payment is not received immediately.

Please contact us urgently to resolve this matter.

Urgent regards,
HCL Software Team`
        },
        thank_you: {
            subject: `Payment Received: ${RENEWAL_TYPES[renewal.renewalType]} Renewal Confirmed`,
            message: `Dear ${customer.name},

Thank you! We have successfully received your payment for the ${RENEWAL_TYPES[renewal.renewalType].toLowerCase()} renewal.

Payment Details:
- Type: ${RENEWAL_TYPES[renewal.renewalType]}
- Amount: $${renewal.amount.toLocaleString()}
- Payment Date: ${renewal.paymentDate ? formatDate(renewal.paymentDate) : 'Today'}

Your services will continue uninterrupted. Thank you for your continued business.

Best regards,
HCL Software Team`
        },
        custom: {
            subject: `Regarding Your ${RENEWAL_TYPES[renewal.renewalType]} Renewal`,
            message: `Dear ${customer.name},

[Please enter your custom message here]

Renewal Details:
- Type: ${RENEWAL_TYPES[renewal.renewalType]}
- Amount: $${renewal.amount.toLocaleString()}
- Due Date: ${formatDate(renewal.renewalDate)}

Best regards,
HCL Software Team`
        }
    };

    const template = templates[notificationType];
    if (template) {
        subjectField.value = template.subject;
        messageField.value = template.message;
    }
}

// Preview notification
function previewNotification() {
    const notificationType = document.getElementById('notificationType').value;
    const method = document.getElementById('notificationMethod').value;
    const priority = document.getElementById('notificationPriority').value;
    const subject = document.getElementById('notificationSubject').value;
    const message = document.getElementById('notificationMessage').value;
    const email = document.getElementById('recipientEmail').value;
    const phone = document.getElementById('recipientPhone').value;
    const scheduled = document.getElementById('scheduleNotification').checked;
    const scheduleTime = document.getElementById('scheduleDateTime').value;

    const preview = document.getElementById('notificationPreview');
    const previewContent = document.getElementById('previewContent');

    const priorityColors = {
        normal: '#3b82f6',
        high: '#f59e0b',
        urgent: '#ef4444'
    };

    const methodIcons = {
        email: '📧',
        sms: '📱',
        both: '📧📱',
        portal: '🔔'
    };

    previewContent.innerHTML = `
        <div class="preview-header" style="background: ${priorityColors[priority]}; color: white; padding: 1rem; border-radius: 5px; margin-bottom: 1rem;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span><strong>${methodIcons[method]} ${method.toUpperCase()}</strong></span>
                <span class="priority-badge">${priority.toUpperCase()} PRIORITY</span>
            </div>
        </div>

        <div class="preview-details">
            <p><strong>To:</strong> ${email}${phone && method !== 'email' ? ` / ${phone}` : ''}</p>
            <p><strong>Subject:</strong> ${subject}</p>
            ${scheduled ? `<p><strong>Scheduled:</strong> ${new Date(scheduleTime).toLocaleString()}</p>` : ''}
        </div>

        <div class="preview-message" style="background: #f8f9fa; padding: 1rem; border-radius: 5px; margin-top: 1rem; white-space: pre-line;">
            ${message}
        </div>
    `;

    preview.style.display = 'block';
}

// Send notification
function sendNotification(event, renewalId) {
    event.preventDefault();

    const notificationType = document.getElementById('notificationType').value;
    const method = document.getElementById('notificationMethod').value;
    const priority = document.getElementById('notificationPriority').value;
    const email = document.getElementById('recipientEmail').value;
    const phone = document.getElementById('recipientPhone').value;
    const subject = document.getElementById('notificationSubject').value;
    const message = document.getElementById('notificationMessage').value;
    const scheduled = document.getElementById('scheduleNotification').checked;
    const scheduleTime = document.getElementById('scheduleDateTime').value;

    // Validate required fields
    if (!email && (method === 'email' || method === 'both')) {
        showNotification('Email address is required for email notifications.', 'error');
        return;
    }

    if (!phone && (method === 'sms' || method === 'both')) {
        showNotification('Phone number is required for SMS notifications.', 'error');
        return;
    }

    if (scheduled && !scheduleTime) {
        showNotification('Please select a schedule date and time.', 'error');
        return;
    }

    // Create notification record
    const notification = {
        id: `NOT-${Date.now()}`,
        renewalId: renewalId,
        type: notificationType,
        method: method,
        priority: priority,
        recipient: {
            email: email,
            phone: phone
        },
        subject: subject,
        message: message,
        scheduled: scheduled,
        scheduleTime: scheduled ? scheduleTime : null,
        sent: !scheduled,
        sentTime: !scheduled ? new Date().toISOString() : null,
        createdBy: getCurrentUser().id,
        created: new Date().toISOString()
    };

    // Add to notifications (you would implement this in your data structure)
    addNotificationRecord(notification);

    // Simulate sending
    if (scheduled) {
        showNotification(`Notification scheduled for ${new Date(scheduleTime).toLocaleString()}`, 'success');
    } else {
        showNotification(`Notification sent successfully via ${method}!`, 'success');
    }

    closeModal();

    // Update renewal record with notification sent flag
    const renewal = getRenewalById(renewalId);
    if (renewal) {
        renewal.lastNotificationSent = new Date().toISOString();
        renewal.notificationCount = (renewal.notificationCount || 0) + 1;
        saveToLocalStorage();
    }
}

// Export renewals
function exportRenewals() {
    const renewals = filteredRenewals;
    if (renewals.length === 0) {
        showNotification('No renewals to export.', 'info');
        return;
    }

    // Create CSV content
    const headers = ['Renewal ID', 'Customer', 'Type', 'Amount', 'Renewal Date', 'Status', 'Payment Date', 'Description'];
    const csvContent = [
        headers.join(','),
        ...renewals.map(renewal => [
            renewal.id,
            `"${renewal.customerName}"`,
            RENEWAL_TYPES[renewal.renewalType],
            renewal.amount,
            renewal.renewalDate,
            RENEWAL_STATUSES[renewal.status],
            renewal.paymentDate || '',
            `"${renewal.description}"`
        ].join(','))
    ].join('\n');

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `renewals_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    showNotification('Renewals exported successfully!', 'success');
}

// Bulk notification functionality
function sendBulkNotifications() {
    const renewals = getRenewals();
    const pendingRenewals = renewals.filter(r => r.status === 'pending' || r.status === 'overdue');

    if (pendingRenewals.length === 0) {
        showNotification('No pending renewals found for notifications.', 'info');
        return;
    }

    const modalBody = document.getElementById('modalBody');
    modalBody.innerHTML = `
        <h2>Send Bulk Notifications</h2>
        <div class="bulk-notification-setup">
            <div class="bulk-summary">
                <h3><i class="fas fa-info-circle"></i> Notification Summary</h3>
                <p>Found <strong>${pendingRenewals.length}</strong> renewals eligible for notifications:</p>
                <ul class="renewal-categories">
                    <li>🔴 Overdue: <strong>${pendingRenewals.filter(r => r.status === 'overdue').length}</strong></li>
                    <li>⚠️ Due within 7 days: <strong>${pendingRenewals.filter(r => r.status === 'pending' && getDaysUntilDue(r.renewalDate) <= 7 && getDaysUntilDue(r.renewalDate) > 0).length}</strong></li>
                    <li>📅 Due within 30 days: <strong>${pendingRenewals.filter(r => r.status === 'pending' && getDaysUntilDue(r.renewalDate) <= 30 && getDaysUntilDue(r.renewalDate) > 7).length}</strong></li>
                    <li>📋 Other pending: <strong>${pendingRenewals.filter(r => r.status === 'pending' && getDaysUntilDue(r.renewalDate) > 30).length}</strong></li>
                </ul>
            </div>

            <form id="bulkNotificationForm" onsubmit="sendBulkNotificationsBatch(event)">
                <div class="form-group">
                    <label>Select Notification Categories:</label>
                    <div class="notification-categories">
                        <label class="category-option">
                            <input type="checkbox" id="notifyOverdue" checked>
                            <span>🔴 Overdue Renewals (${pendingRenewals.filter(r => r.status === 'overdue').length})</span>
                        </label>
                        <label class="category-option">
                            <input type="checkbox" id="notifyUrgent" checked>
                            <span>⚠️ Due within 7 days (${pendingRenewals.filter(r => r.status === 'pending' && getDaysUntilDue(r.renewalDate) <= 7 && getDaysUntilDue(r.renewalDate) > 0).length})</span>
                        </label>
                        <label class="category-option">
                            <input type="checkbox" id="notifyReminder">
                            <span>📅 Due within 30 days (${pendingRenewals.filter(r => r.status === 'pending' && getDaysUntilDue(r.renewalDate) <= 30 && getDaysUntilDue(r.renewalDate) > 7).length})</span>
                        </label>
                        <label class="category-option">
                            <input type="checkbox" id="notifyAll">
                            <span>📋 All pending renewals (${pendingRenewals.filter(r => r.status === 'pending' && getDaysUntilDue(r.renewalDate) > 30).length})</span>
                        </label>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="bulkNotificationMethod">Delivery Method:</label>
                        <select id="bulkNotificationMethod" required>
                            <option value="email">📧 Email Only</option>
                            <option value="sms">📱 SMS Only</option>
                            <option value="both">📧📱 Email & SMS</option>
                            <option value="portal">🔔 Portal Notification</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="bulkNotificationPriority">Priority:</label>
                        <select id="bulkNotificationPriority" required>
                            <option value="normal">🔵 Normal</option>
                            <option value="high">🟡 High</option>
                            <option value="urgent">🔴 Urgent</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="bulkScheduleNotification">
                        Schedule for later delivery
                    </label>
                </div>

                <div class="form-group" id="bulkScheduleGroup" style="display: none;">
                    <label for="bulkScheduleDateTime">Schedule Date & Time:</label>
                    <input type="datetime-local" id="bulkScheduleDateTime">
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="useCustomTemplate">
                        Use custom message template
                    </label>
                </div>

                <div class="form-group" id="customTemplateGroup" style="display: none;">
                    <label for="customSubjectTemplate">Custom Subject Template:</label>
                    <input type="text" id="customSubjectTemplate" placeholder="Use {customerName}, {renewalType}, {amount}, {dueDate}">

                    <label for="customMessageTemplate">Custom Message Template:</label>
                    <textarea id="customMessageTemplate" rows="6" placeholder="Use {customerName}, {renewalType}, {amount}, {dueDate}, {description}"></textarea>

                    <small class="template-help">
                        Available variables: {customerName}, {renewalType}, {amount}, {dueDate}, {description}, {daysUntilDue}
                    </small>
                </div>

                <div class="notification-preview" id="bulkNotificationPreview" style="display: none;">
                    <h4><i class="fas fa-eye"></i> Preview</h4>
                    <div class="preview-content" id="bulkPreviewContent"></div>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-outline" onclick="previewBulkNotifications()">
                        <i class="fas fa-eye"></i> Preview
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i> Send Notifications
                    </button>
                </div>
            </form>
        </div>
    `;

    // Add event listeners
    document.getElementById('bulkScheduleNotification').addEventListener('change', function() {
        const scheduleGroup = document.getElementById('bulkScheduleGroup');
        if (this.checked) {
            scheduleGroup.style.display = 'block';
            const defaultTime = new Date();
            defaultTime.setHours(defaultTime.getHours() + 1);
            document.getElementById('bulkScheduleDateTime').value = defaultTime.toISOString().slice(0, 16);
        } else {
            scheduleGroup.style.display = 'none';
        }
    });

    document.getElementById('useCustomTemplate').addEventListener('change', function() {
        const templateGroup = document.getElementById('customTemplateGroup');
        templateGroup.style.display = this.checked ? 'block' : 'none';
    });

    showModal();
}

// Preview bulk notifications
function previewBulkNotifications() {
    const renewals = getRenewals();
    const pendingRenewals = renewals.filter(r => r.status === 'pending' || r.status === 'overdue');

    // Get selected categories
    const categories = [];
    if (document.getElementById('notifyOverdue').checked) {
        categories.push(...pendingRenewals.filter(r => r.status === 'overdue'));
    }
    if (document.getElementById('notifyUrgent').checked) {
        categories.push(...pendingRenewals.filter(r => r.status === 'pending' && getDaysUntilDue(r.renewalDate) <= 7 && getDaysUntilDue(r.renewalDate) > 0));
    }
    if (document.getElementById('notifyReminder').checked) {
        categories.push(...pendingRenewals.filter(r => r.status === 'pending' && getDaysUntilDue(r.renewalDate) <= 30 && getDaysUntilDue(r.renewalDate) > 7));
    }
    if (document.getElementById('notifyAll').checked) {
        categories.push(...pendingRenewals.filter(r => r.status === 'pending' && getDaysUntilDue(r.renewalDate) > 30));
    }

    const method = document.getElementById('bulkNotificationMethod').value;
    const priority = document.getElementById('bulkNotificationPriority').value;
    const scheduled = document.getElementById('bulkScheduleNotification').checked;
    const scheduleTime = document.getElementById('bulkScheduleDateTime').value;
    const useCustom = document.getElementById('useCustomTemplate').checked;

    const preview = document.getElementById('bulkNotificationPreview');
    const previewContent = document.getElementById('bulkPreviewContent');

    const priorityColors = {
        normal: '#3b82f6',
        high: '#f59e0b',
        urgent: '#ef4444'
    };

    const methodIcons = {
        email: '📧',
        sms: '📱',
        both: '📧📱',
        portal: '🔔'
    };

    previewContent.innerHTML = `
        <div class="preview-header" style="background: ${priorityColors[priority]}; color: white; padding: 1rem; border-radius: 5px; margin-bottom: 1rem;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span><strong>${methodIcons[method]} BULK ${method.toUpperCase()}</strong></span>
                <span class="priority-badge">${priority.toUpperCase()} PRIORITY</span>
            </div>
        </div>

        <div class="preview-details">
            <p><strong>Recipients:</strong> ${categories.length} customers</p>
            <p><strong>Method:</strong> ${method}</p>
            ${scheduled ? `<p><strong>Scheduled:</strong> ${new Date(scheduleTime).toLocaleString()}</p>` : ''}
            <p><strong>Template:</strong> ${useCustom ? 'Custom' : 'Auto-generated based on renewal status'}</p>
        </div>

        <div class="preview-recipients" style="background: #f8f9fa; padding: 1rem; border-radius: 5px; margin-top: 1rem;">
            <h5>Sample Recipients:</h5>
            ${categories.slice(0, 3).map(renewal => `
                <div style="margin-bottom: 0.5rem;">
                    <strong>${renewal.customerName}</strong> - ${RENEWAL_TYPES[renewal.renewalType]}
                    (${renewal.status === 'overdue' ? 'OVERDUE' : `Due in ${getDaysUntilDue(renewal.renewalDate)} days`})
                </div>
            `).join('')}
            ${categories.length > 3 ? `<div>... and ${categories.length - 3} more</div>` : ''}
        </div>
    `;

    preview.style.display = 'block';
}

// Send bulk notifications
function sendBulkNotificationsBatch(event) {
    event.preventDefault();

    const renewals = getRenewals();
    const pendingRenewals = renewals.filter(r => r.status === 'pending' || r.status === 'overdue');

    // Get selected categories
    const selectedRenewals = [];
    if (document.getElementById('notifyOverdue').checked) {
        selectedRenewals.push(...pendingRenewals.filter(r => r.status === 'overdue'));
    }
    if (document.getElementById('notifyUrgent').checked) {
        selectedRenewals.push(...pendingRenewals.filter(r => r.status === 'pending' && getDaysUntilDue(r.renewalDate) <= 7 && getDaysUntilDue(r.renewalDate) > 0));
    }
    if (document.getElementById('notifyReminder').checked) {
        selectedRenewals.push(...pendingRenewals.filter(r => r.status === 'pending' && getDaysUntilDue(r.renewalDate) <= 30 && getDaysUntilDue(r.renewalDate) > 7));
    }
    if (document.getElementById('notifyAll').checked) {
        selectedRenewals.push(...pendingRenewals.filter(r => r.status === 'pending' && getDaysUntilDue(r.renewalDate) > 30));
    }

    if (selectedRenewals.length === 0) {
        showNotification('Please select at least one notification category.', 'error');
        return;
    }

    const method = document.getElementById('bulkNotificationMethod').value;
    const priority = document.getElementById('bulkNotificationPriority').value;
    const scheduled = document.getElementById('bulkScheduleNotification').checked;
    const scheduleTime = document.getElementById('bulkScheduleDateTime').value;
    const useCustom = document.getElementById('useCustomTemplate').checked;

    if (scheduled && !scheduleTime) {
        showNotification('Please select a schedule date and time.', 'error');
        return;
    }

    // Process each renewal
    let successCount = 0;
    selectedRenewals.forEach(renewal => {
        const customer = getCustomerById(renewal.customerId);
        if (!customer) return;

        // Create notification record
        const notification = {
            id: `NOT-${Date.now()}-${renewal.id}`,
            renewalId: renewal.id,
            type: renewal.status === 'overdue' ? 'overdue' : (getDaysUntilDue(renewal.renewalDate) <= 7 ? 'urgent' : 'reminder'),
            method: method,
            priority: priority,
            recipient: {
                email: customer.email,
                phone: customer.phone
            },
            subject: useCustom ? generateCustomSubject(renewal, customer) : generateAutoSubject(renewal),
            message: useCustom ? generateCustomMessage(renewal, customer) : generateAutoMessage(renewal, customer),
            scheduled: scheduled,
            scheduleTime: scheduled ? scheduleTime : null,
            sent: !scheduled,
            sentTime: !scheduled ? new Date().toISOString() : null,
            createdBy: getCurrentUser().id,
            created: new Date().toISOString(),
            bulk: true
        };

        addNotificationRecord(notification);

        // Update renewal record
        renewal.lastNotificationSent = new Date().toISOString();
        renewal.notificationCount = (renewal.notificationCount || 0) + 1;

        successCount++;
    });

    saveToLocalStorage();
    closeModal();

    if (scheduled) {
        showNotification(`${successCount} notifications scheduled for ${new Date(scheduleTime).toLocaleString()}`, 'success');
    } else {
        showNotification(`${successCount} notifications sent successfully via ${method}!`, 'success');
    }
}

// Helper functions for bulk notifications
function generateCustomSubject(renewal, customer) {
    const template = document.getElementById('customSubjectTemplate').value;
    return template
        .replace(/{customerName}/g, customer.name)
        .replace(/{renewalType}/g, RENEWAL_TYPES[renewal.renewalType])
        .replace(/{amount}/g, `$${renewal.amount.toLocaleString()}`)
        .replace(/{dueDate}/g, formatDate(renewal.renewalDate));
}

function generateCustomMessage(renewal, customer) {
    const template = document.getElementById('customMessageTemplate').value;
    return template
        .replace(/{customerName}/g, customer.name)
        .replace(/{renewalType}/g, RENEWAL_TYPES[renewal.renewalType])
        .replace(/{amount}/g, `$${renewal.amount.toLocaleString()}`)
        .replace(/{dueDate}/g, formatDate(renewal.renewalDate))
        .replace(/{description}/g, renewal.description)
        .replace(/{daysUntilDue}/g, getDaysUntilDue(renewal.renewalDate));
}

function generateAutoSubject(renewal) {
    if (renewal.status === 'overdue') {
        return `OVERDUE: ${RENEWAL_TYPES[renewal.renewalType]} Payment Required`;
    } else if (getDaysUntilDue(renewal.renewalDate) <= 7) {
        return `URGENT: ${RENEWAL_TYPES[renewal.renewalType]} Renewal Due Soon`;
    } else {
        return `Renewal Reminder: ${RENEWAL_TYPES[renewal.renewalType]} Due ${formatDate(renewal.renewalDate)}`;
    }
}

function generateAutoMessage(renewal, customer) {
    const daysUntilDue = getDaysUntilDue(renewal.renewalDate);

    if (renewal.status === 'overdue') {
        return `Dear ${customer.name},

OVERDUE NOTICE: Your ${RENEWAL_TYPES[renewal.renewalType].toLowerCase()} payment is now overdue.

Renewal Details:
- Type: ${RENEWAL_TYPES[renewal.renewalType]}
- Amount: $${renewal.amount.toLocaleString()}
- Due Date: ${formatDate(renewal.renewalDate)}
- Days Overdue: ${Math.abs(daysUntilDue)}

Your services may be suspended if payment is not received immediately.

Please contact us urgently to resolve this matter.

Urgent regards,
HCL Software Team`;
    } else if (daysUntilDue <= 7) {
        return `Dear ${customer.name},

URGENT NOTICE: Your ${RENEWAL_TYPES[renewal.renewalType].toLowerCase()} renewal is due very soon.

Renewal Details:
- Type: ${RENEWAL_TYPES[renewal.renewalType]}
- Amount: $${renewal.amount.toLocaleString()}
- Due Date: ${formatDate(renewal.renewalDate)}
- Days Remaining: ${daysUntilDue}

IMMEDIATE ACTION REQUIRED to avoid service disruption.

Please contact us immediately to process your renewal.

Urgent regards,
HCL Software Team`;
    } else {
        return `Dear ${customer.name},

This is a friendly reminder that your ${RENEWAL_TYPES[renewal.renewalType].toLowerCase()} is due for renewal.

Renewal Details:
- Type: ${RENEWAL_TYPES[renewal.renewalType]}
- Amount: $${renewal.amount.toLocaleString()}
- Due Date: ${formatDate(renewal.renewalDate)}
- Days Remaining: ${daysUntilDue}

Description: ${renewal.description}

Please ensure timely payment to avoid any service interruptions.

Best regards,
HCL Software Team`;
    }
}
