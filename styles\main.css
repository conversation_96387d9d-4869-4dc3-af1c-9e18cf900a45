/* Modern CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* HCL Software Brand Colors */
    --primary-color: #0066cc;
    --primary-dark: #004499;
    --primary-light: #3385d6;
    --secondary-color: #00a86b;
    --accent-color: #ff6b35;

    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* Status Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;

    /* Typography */
    --font-sans: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
}

body {
    font-family: var(--font-sans);
    background-color: var(--gray-50);
    color: var(--gray-800);
    line-height: 1.6;
    font-size: 14px;
    overflow-x: hidden;
}

/* Layout Structure */
#app {
    display: flex;
    min-height: 100vh;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    position: fixed;
    top: var(--space-4);
    left: var(--space-4);
    z-index: 1001;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: var(--space-3);
    border-radius: var(--radius);
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
    background: var(--primary-dark);
    transform: scale(1.05);
}

/* Sidebar Navigation */
.sidebar {
    width: 280px;
    background: var(--white);
    border-right: 1px solid var(--gray-200);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 1000;
    transition: transform 0.3s ease;
    box-shadow: var(--shadow-lg);
}

.sidebar-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
}

.company-logo {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.company-logo i {
    font-size: 2rem;
    color: var(--primary-color);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.company-info h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.platform-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-lg);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    background: var(--gray-100);
    color: var(--gray-600);
}

.platform-badge.gold {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: var(--white);
}

.platform-badge.platinum {
    background: linear-gradient(135deg, #9ca3af, #6b7280);
    color: var(--white);
}

.platform-badge.silver {
    background: linear-gradient(135deg, #e5e7eb, #d1d5db);
    color: var(--gray-700);
}

/* Sidebar Navigation */
.sidebar-nav {
    flex: 1;
    padding: var(--space-4) 0;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-6);
    color: var(--gray-600);
    text-decoration: none;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
    font-weight: 500;
}

.nav-link:hover {
    background: var(--gray-50);
    color: var(--primary-color);
    border-left-color: var(--primary-light);
}

.nav-link.active {
    background: var(--primary-color);
    color: var(--white);
    border-left-color: var(--primary-dark);
}

.nav-link i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

/* Sidebar Footer */
.sidebar-footer {
    padding: var(--space-6);
    border-top: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    flex: 1;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.1rem;
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 0.9rem;
}

.user-role {
    font-size: 0.8rem;
    color: var(--gray-500);
}

.logout-btn {
    background: var(--gray-100);
    border: none;
    padding: var(--space-2);
    border-radius: var(--radius);
    cursor: pointer;
    color: var(--gray-600);
    transition: all 0.2s ease;
}

.logout-btn:hover {
    background: var(--error);
    color: var(--white);
}

/* Main Content Area */
.main-content {
    flex: 1;
    margin-left: 280px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Top Header */
.top-header {
    background: var(--white);
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left h2 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.header-left p {
    color: var(--gray-500);
    font-size: 0.9rem;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.header-btn {
    position: relative;
    background: var(--gray-100);
    border: none;
    padding: var(--space-3);
    border-radius: var(--radius);
    cursor: pointer;
    color: var(--gray-600);
    transition: all 0.2s ease;
}

.header-btn:hover {
    background: var(--primary-color);
    color: var(--white);
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--error);
    color: var(--white);
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

/* Content Sections */
.content-section {
    display: none;
    padding: var(--space-6);
    flex: 1;
}

.content-section.active {
    display: block;
}

/* Section Headers */
.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-8);
}

.section-title h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-1);
}

.section-title p {
    color: var(--gray-500);
    font-size: 0.9rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.stat-card {
    background: var(--white);
    padding: var(--space-6);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: var(--space-4);
    transition: all 0.3s ease;
    border: 1px solid var(--gray-200);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--white);
}

.stat-icon.pending {
    background: linear-gradient(135deg, var(--warning), #f97316);
}

.stat-icon.customers {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

.stat-icon.orders {
    background: linear-gradient(135deg, var(--secondary-color), #059669);
}

.stat-icon.revenue {
    background: linear-gradient(135deg, var(--accent-color), #ea580c);
}

.stat-content {
    flex: 1;
}

.stat-content h3 {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--gray-600);
    margin-bottom: var(--space-1);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-number {
    font-size: 2rem;
    font-weight: 800;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.stat-change {
    font-size: 0.8rem;
    font-weight: 500;
}

.stat-change.positive {
    color: var(--success);
}

.stat-change.negative {
    color: var(--error);
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-6);
}

.dashboard-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    box-shadow: var(--shadow-lg);
}

.card-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.card-content {
    padding: var(--space-6);
}

.platform-tier {
    background: var(--gray-100);
    color: var(--gray-600);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius);
    font-size: 0.8rem;
    font-weight: 600;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-5);
    border: none;
    border-radius: var(--radius);
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--gray-500);
    color: var(--white);
}

.btn-secondary:hover {
    background: var(--gray-600);
}

.btn-success {
    background: var(--success);
    color: var(--white);
}

.btn-success:hover {
    background: #059669;
}

.btn-warning {
    background: var(--warning);
    color: var(--white);
}

.btn-warning:hover {
    background: #d97706;
}

.btn-danger {
    background: var(--error);
    color: var(--white);
}

.btn-danger:hover {
    background: #dc2626;
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* Timeline Section */
.timeline-section {
    margin: var(--space-8) 0;
}

.timeline-section-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: var(--space-6);
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.timeline-section-title i {
    color: var(--primary-color);
}

.timeline-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-6);
}

/* Timeline Cards */
.timeline-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

.timeline-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.timeline-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.timeline-card.today-card::before {
    background: linear-gradient(90deg, #4caf50, #66bb6a);
}

.timeline-card.weekly-card::before {
    background: linear-gradient(90deg, #2196f3, #42a5f5);
}

.timeline-card.mtd-card::before {
    background: linear-gradient(90deg, #ff9800, #ffb74d);
}

.timeline-card.ytd-card::before {
    background: linear-gradient(90deg, #9c27b0, #ba68c8);
}

/* Timeline Header */
.timeline-header {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    margin-bottom: var(--space-5);
}

.timeline-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: var(--white);
    flex-shrink: 0;
}

.timeline-icon.today {
    background: linear-gradient(135deg, #4caf50, #66bb6a);
}

.timeline-icon.weekly {
    background: linear-gradient(135deg, #2196f3, #42a5f5);
}

.timeline-icon.mtd {
    background: linear-gradient(135deg, #ff9800, #ffb74d);
}

.timeline-icon.ytd {
    background: linear-gradient(135deg, #9c27b0, #ba68c8);
}

.timeline-info {
    flex: 1;
}

.timeline-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 var(--space-1) 0;
    color: var(--gray-900);
}

.timeline-subtitle {
    font-size: 0.875rem;
    color: var(--gray-500);
    margin: 0;
}

/* Timeline Stats */
.timeline-stats {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: var(--space-5);
}

.timeline-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--gray-900);
    line-height: 1;
}

.timeline-target {
    font-size: 0.875rem;
    color: var(--gray-500);
    font-weight: 500;
}

.timeline-target span {
    color: var(--primary-color);
    font-weight: 600;
}

/* Timeline Progress */
.timeline-progress {
    margin-top: var(--space-4);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--space-2);
    position: relative;
}

.progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 1s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.today-progress {
    background: linear-gradient(90deg, #4caf50, #66bb6a);
}

.weekly-progress {
    background: linear-gradient(90deg, #2196f3, #42a5f5);
}

.mtd-progress {
    background: linear-gradient(90deg, #ff9800, #ffb74d);
}

.ytd-progress {
    background: linear-gradient(90deg, #9c27b0, #ba68c8);
}

.progress-label {
    font-size: 0.8rem;
    color: var(--gray-500);
    font-weight: 500;
    text-align: center;
}

/* Section Headers */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.section-header h2 {
    color: #333;
    font-size: 1.8rem;
}

/* Filters */
.filters {
    background: white;
    padding: 1rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    display: flex;
    gap: 1rem;
}

.filters select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    background: white;
}

/* Tables */
.po-table-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.po-table {
    width: 100%;
    border-collapse: collapse;
}

.po-table th,
.po-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.po-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.po-table tr:hover {
    background: #f8f9fa;
}

/* Status badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-draft {
    background: #e9ecef;
    color: #6c757d;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-approved {
    background: #d4edda;
    color: #155724;
}

.status-rejected {
    background: #f8d7da;
    color: #721c24;
}

/* Customer Types */
.customer-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.customer-type-card {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    cursor: pointer;
    transition: transform 0.3s;
}

.customer-type-card:hover {
    transform: translateY(-5px);
}

.customer-count {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
    margin: 1rem 0;
}

.renewal-types {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

.renewal-badge {
    background: #e9ecef;
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
    font-size: 0.7rem;
    color: #6c757d;
}

/* Admin Panel */
.admin-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.admin-card {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.role-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #eee;
}

.role-count {
    background: #667eea;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.setting-item {
    margin-bottom: 1rem;
}

.setting-item label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.setting-item select,
.setting-item input {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    width: 100%;
}

/* Role-based visibility */
.admin-only {
    display: none;
}

body.admin .admin-only {
    display: block;
}

body.admin .nav-link.admin-only {
    display: inline-block;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-4);
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-5);
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    color: var(--gray-700);
}

.quick-action-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.quick-action-btn i {
    font-size: 1.5rem;
}

.quick-action-btn span {
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
}

/* Customer Distribution */
.customer-distribution {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.distribution-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-3);
    background: var(--gray-50);
    border-radius: var(--radius);
}

.distribution-label {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-weight: 500;
}

.distribution-count {
    font-weight: 700;
    color: var(--primary-color);
}

/* Mobile Overlay */
.mobile-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

/* Role-based visibility */
.admin-only {
    display: none;
}

body.admin .admin-only {
    display: block;
}

body.admin .nav-link.admin-only {
    display: flex;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }

    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .mobile-overlay.show {
        display: block;
    }

    .main-content {
        margin-left: 0;
        width: 100%;
    }

    .top-header {
        padding: var(--space-4);
        margin-left: 0;
    }

    .header-left h2 {
        font-size: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }

    .stat-card {
        padding: var(--space-4);
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-4);
    }

    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }

    .quick-action-btn {
        padding: var(--space-4);
    }

    .content-section {
        padding: var(--space-4);
    }
}

@media (max-width: 480px) {
    .stats-grid {
        gap: var(--space-3);
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: var(--space-3);
    }

    .quick-actions {
        grid-template-columns: 1fr;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }

    .section-header .btn {
        width: auto;
    }

    /* Timeline responsive */
    .timeline-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }

    .timeline-card {
        padding: var(--space-4);
    }

    .timeline-header {
        gap: var(--space-3);
    }

    .timeline-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .timeline-number {
        font-size: 2rem;
    }

    .timeline-stats {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-2);
    }
}
