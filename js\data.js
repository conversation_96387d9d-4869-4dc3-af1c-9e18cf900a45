// Data structure and mock data for the company portal

// Platform configurations
const PLATFORMS = {
    silver: {
        name: 'Silver',
        features: [
            'Basic Purchase Orders',
            'Customer Management',
            'Basic Reporting',
            'Email Support'
        ],
        maxUsers: 10,
        maxPOs: 50,
        approvalLevels: 1
    },
    gold: {
        name: 'Gold',
        features: [
            'Advanced Purchase Orders',
            'Customer Management',
            'Advanced Reporting',
            'Workflow Automation',
            'Priority Support',
            'API Access'
        ],
        maxUsers: 50,
        maxPOs: 200,
        approvalLevels: 2
    },
    platinum: {
        name: 'Platinum',
        features: [
            'Enterprise Purchase Orders',
            'Advanced Customer Management',
            'Custom Reporting',
            'Advanced Workflow Automation',
            'Multi-level Approvals',
            'Dedicated Support',
            'Full API Access',
            'Custom Integrations'
        ],
        maxUsers: -1, // Unlimited
        maxPOs: -1, // Unlimited
        approvalLevels: 3
    }
};

// User roles and permissions
const USER_ROLES = {
    admin: {
        name: 'Admin',
        permissions: ['read', 'write', 'approve', 'admin', 'delete']
    },
    read: {
        name: 'Read Only',
        permissions: ['read']
    },
    readwrite: {
        name: 'Read & Write',
        permissions: ['read', 'write']
    }
};

// Customer types
const CUSTOMER_TYPES = {
    NN: {
        name: 'New Normal',
        description: 'New customers with standard requirements',
        renewalTypes: []
    },
    EN: {
        name: 'Existing Normal',
        description: 'Existing customers with standard requirements',
        renewalTypes: []
    },
    EE: {
        name: 'Existing Enterprise',
        description: 'Existing enterprise customers with complex requirements',
        renewalTypes: ['license', 'support_activity', 'cr']
    }
};

// Renewal types for EE customers
const RENEWAL_TYPES = {
    license: 'License Renewal',
    support_activity: 'Support Activity Renewal',
    cr: 'Change Request (CR) Renewal'
};

// Renewal statuses
const RENEWAL_STATUSES = {
    pending: 'Pending',
    received: 'Received',
    overdue: 'Overdue',
    cancelled: 'Cancelled'
};

// Purchase Order statuses
const PO_STATUSES = {
    draft: 'Draft',
    pending: 'Pending Approval',
    approved: 'Approved',
    rejected: 'Rejected',
    completed: 'Completed'
};

// Helper function to get date X days ago
function getDateDaysAgo(days) {
    const date = new Date();
    date.setDate(date.getDate() - days);
    return date.toISOString().split('T')[0];
}

// Mock data
let mockData = {
    currentUser: {
        id: 1,
        name: 'John Admin',
        email: '<EMAIL>',
        role: 'admin',
        platform: 'platinum'
    },
    
    users: [
        {
            id: 1,
            name: 'John Admin',
            email: '<EMAIL>',
            role: 'admin',
            platform: 'platinum',
            active: true
        },
        {
            id: 2,
            name: 'Jane Manager',
            email: '<EMAIL>',
            role: 'readwrite',
            platform: 'gold',
            active: true
        },
        {
            id: 3,
            name: 'Bob Viewer',
            email: '<EMAIL>',
            role: 'read',
            platform: 'silver',
            active: true
        }
    ],
    
    customers: [
        {
            id: 1,
            name: 'Acme Corp',
            type: 'EE',
            email: '<EMAIL>',
            phone: '******-0101',
            renewalTypes: ['license', 'support_activity'],
            active: true,
            created: '2024-01-15'
        },
        {
            id: 2,
            name: 'TechStart Inc',
            type: 'NN',
            email: '<EMAIL>',
            phone: '******-0102',
            renewalTypes: [],
            active: true,
            created: '2024-02-20'
        },
        {
            id: 3,
            name: 'Global Solutions',
            type: 'EN',
            email: '<EMAIL>',
            phone: '******-0103',
            renewalTypes: [],
            active: true,
            created: '2023-08-10'
        },
        {
            id: 4,
            name: 'Enterprise Systems',
            type: 'EE',
            email: '<EMAIL>',
            phone: '******-0104',
            renewalTypes: ['license', 'cr'],
            active: true,
            created: '2023-05-12'
        },
        {
            id: 5,
            name: 'Innovation Labs',
            type: 'NN',
            email: '<EMAIL>',
            phone: '******-0105',
            renewalTypes: [],
            active: true,
            created: '2024-03-01'
        }
    ],
    
    purchaseOrders: [
        {
            id: 'PO-2024-001',
            customerId: 1,
            customerName: 'Acme Corp',
            customerType: 'EE',
            amount: 15000,
            status: 'pending',
            created: '2024-03-01',
            createdBy: 2,
            items: [
                { description: 'Software License', quantity: 10, unitPrice: 1000, total: 10000 },
                { description: 'Support Package', quantity: 1, unitPrice: 5000, total: 5000 }
            ],
            approvals: [],
            notes: 'Annual license renewal for Acme Corp'
        },
        {
            id: 'PO-2024-002',
            customerId: 2,
            customerName: 'TechStart Inc',
            customerType: 'NN',
            amount: 5000,
            status: 'approved',
            created: '2024-02-28',
            createdBy: 2,
            items: [
                { description: 'Starter Package', quantity: 1, unitPrice: 5000, total: 5000 }
            ],
            approvals: [
                { userId: 1, userName: 'John Admin', action: 'approved', date: '2024-03-01', notes: 'Approved for new customer' }
            ],
            notes: 'Initial setup for new customer'
        },
        {
            id: 'PO-2024-003',
            customerId: 3,
            customerName: 'Global Solutions',
            customerType: 'EN',
            amount: 8500,
            status: 'draft',
            created: '2024-03-02',
            createdBy: 2,
            items: [
                { description: 'Additional Licenses', quantity: 5, unitPrice: 1200, total: 6000 },
                { description: 'Training Services', quantity: 1, unitPrice: 2500, total: 2500 }
            ],
            approvals: [],
            notes: 'Expansion package for existing customer'
        },
        // Today's orders
        {
            id: 'PO-2024-004',
            customerId: 1,
            customerName: 'Acme Corp',
            customerType: 'EE',
            amount: 12000,
            status: 'pending',
            created: new Date().toISOString().split('T')[0], // Today
            createdBy: 2,
            items: [
                { description: 'Today License', quantity: 8, unitPrice: 1500, total: 12000 }
            ],
            approvals: [],
            notes: 'Today order for testing'
        },
        {
            id: 'PO-2024-005',
            customerId: 2,
            customerName: 'TechStart Inc',
            customerType: 'EN',
            amount: 9500,
            status: 'approved',
            created: new Date().toISOString().split('T')[0], // Today
            createdBy: 2,
            items: [
                { description: 'Today Support', quantity: 1, unitPrice: 9500, total: 9500 }
            ],
            approvals: [
                { userId: 1, action: 'approved', timestamp: new Date().toISOString() }
            ],
            notes: 'Today approved order'
        },
        // This week's orders
        {
            id: 'PO-2024-006',
            customerId: 3,
            customerName: 'Global Solutions',
            customerType: 'EN',
            amount: 18000,
            status: 'approved',
            created: getDateDaysAgo(2), // 2 days ago
            createdBy: 2,
            items: [
                { description: 'Weekly Package', quantity: 6, unitPrice: 3000, total: 18000 }
            ],
            approvals: [
                { userId: 1, action: 'approved', timestamp: getDateDaysAgo(1) + 'T10:30:00Z' }
            ],
            notes: 'Weekly order'
        },
        {
            id: 'PO-2024-007',
            customerId: 1,
            customerName: 'Acme Corp',
            customerType: 'EE',
            amount: 25000,
            status: 'pending',
            created: getDateDaysAgo(4), // 4 days ago
            createdBy: 2,
            items: [
                { description: 'Enterprise Weekly', quantity: 10, unitPrice: 2500, total: 25000 }
            ],
            approvals: [],
            notes: 'Weekly enterprise order'
        },
        // This month's orders
        {
            id: 'PO-2024-008',
            customerId: 2,
            customerName: 'TechStart Inc',
            customerType: 'EN',
            amount: 35000,
            status: 'approved',
            created: getDateDaysAgo(15), // 15 days ago
            createdBy: 2,
            items: [
                { description: 'Monthly Package', quantity: 14, unitPrice: 2500, total: 35000 }
            ],
            approvals: [
                { userId: 1, action: 'approved', timestamp: getDateDaysAgo(14) + 'T14:20:00Z' }
            ],
            notes: 'Monthly order'
        },
        {
            id: 'PO-2024-009',
            customerId: 3,
            customerName: 'Global Solutions',
            customerType: 'EN',
            amount: 22000,
            status: 'approved',
            created: getDateDaysAgo(20), // 20 days ago
            createdBy: 2,
            items: [
                { description: 'MTD Package', quantity: 11, unitPrice: 2000, total: 22000 }
            ],
            approvals: [
                { userId: 1, action: 'approved', timestamp: getDateDaysAgo(19) + 'T11:45:00Z' }
            ],
            notes: 'Month to date order'
        },
        // Year to date orders
        {
            id: 'PO-2024-010',
            customerId: 1,
            customerName: 'Acme Corp',
            customerType: 'EE',
            amount: 45000,
            status: 'approved',
            created: getDateDaysAgo(60), // 60 days ago
            createdBy: 2,
            items: [
                { description: 'YTD Enterprise', quantity: 18, unitPrice: 2500, total: 45000 }
            ],
            approvals: [
                { userId: 1, action: 'approved', timestamp: getDateDaysAgo(59) + 'T09:15:00Z' }
            ],
            notes: 'Year to date order'
        },
        {
            id: 'PO-2024-011',
            customerId: 2,
            customerName: 'TechStart Inc',
            customerType: 'EN',
            amount: 28000,
            status: 'approved',
            created: getDateDaysAgo(90), // 90 days ago
            createdBy: 2,
            items: [
                { description: 'YTD Support', quantity: 14, unitPrice: 2000, total: 28000 }
            ],
            approvals: [
                { userId: 1, action: 'approved', timestamp: getDateDaysAgo(89) + 'T15:20:00Z' }
            ],
            notes: 'Year to date support order'
        },
        // Additional sample data for better graphs
        {
            id: 'PO-2024-012',
            customerId: 4,
            customerName: 'Enterprise Systems',
            customerType: 'EE',
            amount: 65000,
            status: 'approved',
            created: getDateDaysAgo(30), // 30 days ago
            createdBy: 1,
            items: [
                { description: 'Enterprise License Bundle', quantity: 20, unitPrice: 3250, total: 65000 }
            ],
            approvals: [
                { userId: 1, action: 'approved', timestamp: getDateDaysAgo(29) + 'T10:15:00Z' }
            ],
            notes: 'Large enterprise license purchase'
        },
        {
            id: 'PO-2024-013',
            customerId: 3,
            customerName: 'Global Solutions',
            customerType: 'EN',
            amount: 15500,
            status: 'rejected',
            created: getDateDaysAgo(25), // 25 days ago
            createdBy: 2,
            items: [
                { description: 'Premium Support', quantity: 1, unitPrice: 15500, total: 15500 }
            ],
            approvals: [
                { userId: 1, action: 'rejected', timestamp: getDateDaysAgo(24) + 'T14:30:00Z', notes: 'Budget constraints' }
            ],
            notes: 'Premium support upgrade request'
        },
        {
            id: 'PO-2024-014',
            customerId: 1,
            customerName: 'Acme Corp',
            customerType: 'EE',
            amount: 42000,
            status: 'pending',
            created: getDateDaysAgo(5), // 5 days ago
            createdBy: 2,
            items: [
                { description: 'Q2 License Expansion', quantity: 15, unitPrice: 2800, total: 42000 }
            ],
            approvals: [],
            notes: 'Quarterly license expansion'
        },
        {
            id: 'PO-2024-015',
            customerId: 5,
            customerName: 'Innovation Labs',
            customerType: 'NN',
            amount: 8500,
            status: 'draft',
            created: getDateDaysAgo(1), // 1 day ago
            createdBy: 2,
            items: [
                { description: 'Starter Package Plus', quantity: 1, unitPrice: 8500, total: 8500 }
            ],
            approvals: [],
            notes: 'New customer onboarding package'
        }
    ],

    renewals: [
        {
            id: 'REN-2024-001',
            customerId: 1,
            customerName: 'Acme Corp',
            customerType: 'EE',
            renewalType: 'license',
            amount: 50000,
            renewalDate: '2024-03-15',
            status: 'received',
            paymentDate: '2024-03-10',
            description: 'Annual software license renewal',
            created: '2024-02-15',
            createdBy: 1
        },
        {
            id: 'REN-2024-002',
            customerId: 1,
            customerName: 'Acme Corp',
            customerType: 'EE',
            renewalType: 'support_activity',
            amount: 25000,
            renewalDate: '2024-04-01',
            status: 'pending',
            paymentDate: null,
            description: 'Support activity renewal for Q2',
            created: '2024-03-01',
            createdBy: 2
        },
        {
            id: 'REN-2024-003',
            customerId: 4,
            customerName: 'Enterprise Systems',
            customerType: 'EE',
            renewalType: 'license',
            amount: 75000,
            renewalDate: '2024-03-20',
            status: 'received',
            paymentDate: '2024-03-18',
            description: 'Enterprise license renewal',
            created: '2024-02-20',
            createdBy: 1
        },
        {
            id: 'REN-2024-004',
            customerId: 4,
            customerName: 'Enterprise Systems',
            customerType: 'EE',
            renewalType: 'cr',
            amount: 15000,
            renewalDate: '2024-03-25',
            status: 'pending',
            paymentDate: null,
            description: 'Change request implementation',
            created: '2024-03-05',
            createdBy: 2
        },
        {
            id: 'REN-2024-005',
            customerId: 1,
            customerName: 'Acme Corp',
            customerType: 'EE',
            renewalType: 'support_activity',
            amount: 30000,
            renewalDate: getDateDaysAgo(-30), // 30 days from now
            status: 'pending',
            paymentDate: null,
            description: 'Extended support package',
            created: getDateDaysAgo(5),
            createdBy: 1
        },
        {
            id: 'REN-2024-006',
            customerId: 4,
            customerName: 'Enterprise Systems',
            customerType: 'EE',
            renewalType: 'license',
            amount: 45000,
            renewalDate: getDateDaysAgo(-15), // 15 days from now
            status: 'pending',
            paymentDate: null,
            description: 'Additional license seats',
            created: getDateDaysAgo(10),
            createdBy: 2
        },
        {
            id: 'REN-2024-007',
            customerId: 1,
            customerName: 'Acme Corp',
            customerType: 'EE',
            renewalType: 'cr',
            amount: 20000,
            renewalDate: getDateDaysAgo(5), // 5 days ago (overdue)
            status: 'overdue',
            paymentDate: null,
            description: 'Custom feature development',
            created: getDateDaysAgo(45),
            createdBy: 1
        },
        {
            id: 'REN-2024-008',
            customerId: 4,
            customerName: 'Enterprise Systems',
            customerType: 'EE',
            renewalType: 'support_activity',
            amount: 35000,
            renewalDate: getDateDaysAgo(-7), // 7 days from now
            status: 'pending',
            paymentDate: null,
            description: 'Premium support renewal',
            created: getDateDaysAgo(20),
            createdBy: 2
        },
        {
            id: 'REN-2024-009',
            customerId: 1,
            customerName: 'Acme Corp',
            customerType: 'EE',
            renewalType: 'license',
            amount: 60000,
            renewalDate: getDateDaysAgo(-60), // 60 days from now
            status: 'pending',
            paymentDate: null,
            description: 'Annual enterprise license',
            created: getDateDaysAgo(30),
            createdBy: 1
        },
        {
            id: 'REN-2024-010',
            customerId: 4,
            customerName: 'Enterprise Systems',
            customerType: 'EE',
            renewalType: 'cr',
            amount: 25000,
            renewalDate: getDateDaysAgo(-45), // 45 days from now
            status: 'pending',
            paymentDate: null,
            description: 'System integration changes',
            created: getDateDaysAgo(15),
            createdBy: 2
        }
    ],

    notifications: [
        // Sample notification records will be added here
    ],

    settings: {
        defaultPlatform: 'silver',
        approvalRequired: true,
        emailNotifications: true,
        autoApprovalLimit: 1000
    }
};

// Data access functions
function getCurrentUser() {
    return mockData.currentUser;
}

function getUsers() {
    return mockData.users;
}

function getCustomers() {
    return mockData.customers;
}

function getPurchaseOrders() {
    return mockData.purchaseOrders;
}

function getCustomerById(id) {
    return mockData.customers.find(c => c.id === id);
}

function getPOById(id) {
    return mockData.purchaseOrders.find(po => po.id === id);
}

function getUserById(id) {
    return mockData.users.find(u => u.id === id);
}

function getRenewals() {
    return mockData.renewals;
}

function getRenewalById(id) {
    return mockData.renewals.find(r => r.id === id);
}

function getNotifications() {
    return mockData.notifications;
}

function getNotificationById(id) {
    return mockData.notifications.find(n => n.id === id);
}

// Data manipulation functions
function addCustomer(customer) {
    const newId = Math.max(...mockData.customers.map(c => c.id)) + 1;
    customer.id = newId;
    customer.created = new Date().toISOString().split('T')[0];
    customer.active = true;
    mockData.customers.push(customer);
    return customer;
}

function updateCustomer(id, updates) {
    const index = mockData.customers.findIndex(c => c.id === id);
    if (index !== -1) {
        mockData.customers[index] = { ...mockData.customers[index], ...updates };
        return mockData.customers[index];
    }
    return null;
}

function addPurchaseOrder(po) {
    const newId = `PO-${new Date().getFullYear()}-${String(mockData.purchaseOrders.length + 1).padStart(3, '0')}`;
    po.id = newId;
    po.created = new Date().toISOString().split('T')[0];
    po.status = 'draft';
    po.approvals = [];
    po.createdBy = getCurrentUser().id;
    mockData.purchaseOrders.push(po);
    return po;
}

function updatePurchaseOrder(id, updates) {
    const index = mockData.purchaseOrders.findIndex(po => po.id === id);
    if (index !== -1) {
        mockData.purchaseOrders[index] = { ...mockData.purchaseOrders[index], ...updates };
        return mockData.purchaseOrders[index];
    }
    return null;
}

function approvePurchaseOrder(id, approval) {
    const po = getPOById(id);
    if (po) {
        po.approvals.push({
            ...approval,
            date: new Date().toISOString().split('T')[0]
        });
        po.status = approval.action === 'approved' ? 'approved' : 'rejected';
        return po;
    }
    return null;
}

function addRenewal(renewal) {
    const newId = `REN-${new Date().getFullYear()}-${String(mockData.renewals.length + 1).padStart(3, '0')}`;
    renewal.id = newId;
    renewal.created = new Date().toISOString().split('T')[0];
    renewal.status = renewal.status || 'pending';
    renewal.createdBy = getCurrentUser().id;
    mockData.renewals.push(renewal);
    return renewal;
}

function updateRenewal(id, updates) {
    const index = mockData.renewals.findIndex(r => r.id === id);
    if (index !== -1) {
        mockData.renewals[index] = { ...mockData.renewals[index], ...updates };
        return mockData.renewals[index];
    }
    return null;
}

function updateRenewalStatus(id, status, paymentDate = null) {
    const index = mockData.renewals.findIndex(r => r.id === id);
    if (index !== -1) {
        mockData.renewals[index].status = status;
        if (status === 'received' && paymentDate) {
            mockData.renewals[index].paymentDate = paymentDate;
        }
        return mockData.renewals[index];
    }
    return null;
}

function addNotificationRecord(notification) {
    mockData.notifications.push(notification);
    return notification;
}

function updateNotificationStatus(id, status) {
    const index = mockData.notifications.findIndex(n => n.id === id);
    if (index !== -1) {
        mockData.notifications[index].status = status;
        if (status === 'sent') {
            mockData.notifications[index].sentTime = new Date().toISOString();
        }
        return mockData.notifications[index];
    }
    return null;
}

function getNotificationsByRenewal(renewalId) {
    return mockData.notifications.filter(n => n.renewalId === renewalId);
}

// Statistics functions
function getStats() {
    const pos = getPurchaseOrders();
    const customers = getCustomers();

    return {
        pendingPOs: pos.filter(po => po.status === 'pending').length,
        pendingAmount: pos.filter(po => po.status === 'pending').reduce((sum, po) => sum + po.amount, 0),
        activeCustomers: customers.filter(c => c.active).length,
        monthlyOrders: pos.filter(po => {
            const created = new Date(po.created);
            const now = new Date();
            return created.getMonth() === now.getMonth() && created.getFullYear() === now.getFullYear();
        }).length,
        totalRevenue: pos.filter(po => po.status === 'approved').reduce((sum, po) => sum + po.amount, 0)
    };
}

function getCustomerStats() {
    const customers = getCustomers();
    return {
        NN: customers.filter(c => c.type === 'NN').length,
        EN: customers.filter(c => c.type === 'EN').length,
        EE: customers.filter(c => c.type === 'EE').length
    };
}

function getUserStats() {
    const users = getUsers();
    return {
        admin: users.filter(u => u.role === 'admin').length,
        read: users.filter(u => u.role === 'read').length,
        readwrite: users.filter(u => u.role === 'readwrite').length
    };
}

function getRenewalStats() {
    const renewals = getRenewals();
    const today = new Date().toISOString().split('T')[0];

    // Calculate received vs pending amounts by type
    const stats = {
        license: { received: 0, pending: 0 },
        support_activity: { received: 0, pending: 0 },
        cr: { received: 0, pending: 0 }
    };

    renewals.forEach(renewal => {
        if (renewal.status === 'received') {
            stats[renewal.renewalType].received += renewal.amount;
        } else if (renewal.status === 'pending' || renewal.status === 'overdue') {
            stats[renewal.renewalType].pending += renewal.amount;
        }
    });

    return stats;
}

function getRenewalTimelineStats() {
    const renewals = getRenewals();
    const now = new Date();
    const today = now.toISOString().split('T')[0];

    // Calculate start of week (Monday)
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - now.getDay() + 1);

    // Calculate start of month
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Calculate start of year
    const startOfYear = new Date(now.getFullYear(), 0, 1);

    const stats = {
        today: 0,
        weekly: 0,
        mtd: 0,
        ytd: 0
    };

    renewals.forEach(renewal => {
        if (renewal.status === 'received' && renewal.paymentDate) {
            const paymentDate = new Date(renewal.paymentDate);

            // Today
            if (renewal.paymentDate === today) {
                stats.today += renewal.amount;
            }

            // This week
            if (paymentDate >= startOfWeek) {
                stats.weekly += renewal.amount;
            }

            // Month to date
            if (paymentDate >= startOfMonth) {
                stats.mtd += renewal.amount;
            }

            // Year to date
            if (paymentDate >= startOfYear) {
                stats.ytd += renewal.amount;
            }
        }
    });

    return stats;
}

// Local storage functions
function saveToLocalStorage() {
    localStorage.setItem('companyPortalData', JSON.stringify(mockData));
}

function loadFromLocalStorage() {
    const saved = localStorage.getItem('companyPortalData');
    if (saved) {
        mockData = JSON.parse(saved);
    }
}

// Initialize data from local storage on load
document.addEventListener('DOMContentLoaded', function() {
    loadFromLocalStorage();
});
