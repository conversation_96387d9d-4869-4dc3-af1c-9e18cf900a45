// User management functionality

// Manage users (Admin only)
function manageUsers() {
    if (!auth.isAdmin()) {
        showNotification('Access denied. Admin privileges required.', 'error');
        return;
    }
    
    const users = getUsers();
    const modalBody = document.getElementById('modalBody');
    
    modalBody.innerHTML = `
        <h2>User Management</h2>
        <div class="section-header">
            <h3>System Users</h3>
            <button class="btn btn-primary" onclick="addNewUser()">Add User</button>
        </div>
        
        <div class="user-list">
            ${users.map(user => `
                <div class="user-item">
                    <div>
                        <strong>${user.name}</strong><br>
                        <small>${user.email}</small>
                    </div>
                    <div>
                        <span class="role-badge role-${user.role}">${USER_ROLES[user.role].name}</span>
                    </div>
                    <div>
                        <span class="platform-badge ${user.platform}">${PLATFORMS[user.platform].name}</span>
                    </div>
                    <div>
                        <span class="status-badge ${user.active ? 'status-approved' : 'status-rejected'}">
                            ${user.active ? 'Active' : 'Inactive'}
                        </span>
                    </div>
                    <div class="user-actions">
                        <button class="action-btn action-edit" onclick="editUser(${user.id})">Edit</button>
                        ${user.id !== getCurrentUser().id ? `
                            <button class="action-btn ${user.active ? 'action-reject' : 'action-approve'}" 
                                    onclick="toggleUserStatus(${user.id})">
                                ${user.active ? 'Deactivate' : 'Activate'}
                            </button>
                        ` : ''}
                    </div>
                </div>
            `).join('')}
        </div>
        
        <div class="form-actions" style="margin-top: 2rem;">
            <button type="button" class="btn btn-secondary" onclick="closeModal()">Close</button>
        </div>
    `;
    
    showModal();
}

// Add new user
function addNewUser() {
    const modalBody = document.getElementById('modalBody');
    
    modalBody.innerHTML = `
        <h2>Add New User</h2>
        <form id="addUserForm" onsubmit="saveNewUser(event)">
            <div class="form-row">
                <div class="form-group">
                    <label for="userName">Full Name:</label>
                    <input type="text" id="userName" required placeholder="Enter full name">
                </div>
                <div class="form-group">
                    <label for="userEmail">Email:</label>
                    <input type="email" id="userEmail" required placeholder="<EMAIL>">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="userRole">Role:</label>
                    <select id="userRole" required onchange="updateRoleDescription()">
                        <option value="">Select Role</option>
                        <option value="admin">Admin - Full Access</option>
                        <option value="readwrite">Read & Write - Needs Approval</option>
                        <option value="read">Read Only - View Access</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="userPlatform">Platform Tier:</label>
                    <select id="userPlatform" required onchange="updatePlatformDescription()">
                        <option value="">Select Platform</option>
                        <option value="silver">Silver</option>
                        <option value="gold">Gold</option>
                        <option value="platinum">Platinum</option>
                    </select>
                </div>
            </div>
            
            <div id="roleDescription" class="form-group" style="display: none;">
                <label>Role Permissions:</label>
                <div id="rolePermissions" class="permissions-list"></div>
            </div>
            
            <div id="platformDescription" class="form-group" style="display: none;">
                <label>Platform Features:</label>
                <div id="platformFeatures" class="features-list"></div>
            </div>
            
            <div class="form-group">
                <label for="userPassword">Temporary Password:</label>
                <input type="password" id="userPassword" required placeholder="Enter temporary password">
                <small>User will be required to change password on first login</small>
            </div>
            
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="manageUsers()">Back</button>
                <button type="submit" class="btn btn-primary">Add User</button>
            </div>
        </form>
    `;
    
    showModal();
}

// Update role description
function updateRoleDescription() {
    const roleSelect = document.getElementById('userRole');
    const roleDescription = document.getElementById('roleDescription');
    const rolePermissions = document.getElementById('rolePermissions');
    
    if (roleSelect.value) {
        const role = USER_ROLES[roleSelect.value];
        rolePermissions.innerHTML = `
            <ul>
                ${role.permissions.map(permission => `<li>${formatPermission(permission)}</li>`).join('')}
            </ul>
        `;
        roleDescription.style.display = 'block';
    } else {
        roleDescription.style.display = 'none';
    }
}

// Update platform description
function updatePlatformDescription() {
    const platformSelect = document.getElementById('userPlatform');
    const platformDescription = document.getElementById('platformDescription');
    const platformFeatures = document.getElementById('platformFeatures');
    
    if (platformSelect.value) {
        const platform = PLATFORMS[platformSelect.value];
        platformFeatures.innerHTML = `
            <ul>
                ${platform.features.slice(0, 4).map(feature => `<li>${feature}</li>`).join('')}
                ${platform.features.length > 4 ? `<li><em>+${platform.features.length - 4} more features</em></li>` : ''}
            </ul>
            <p><strong>Limits:</strong> 
               ${platform.maxUsers === -1 ? 'Unlimited' : platform.maxUsers} users, 
               ${platform.maxPOs === -1 ? 'Unlimited' : platform.maxPOs} POs
            </p>
        `;
        platformDescription.style.display = 'block';
    } else {
        platformDescription.style.display = 'none';
    }
}

// Format permission for display
function formatPermission(permission) {
    const permissionNames = {
        'read': 'View all data',
        'write': 'Create and edit records',
        'approve': 'Approve purchase orders',
        'admin': 'Administrative functions',
        'delete': 'Delete records'
    };
    return permissionNames[permission] || permission;
}

// Save new user
function saveNewUser(event) {
    event.preventDefault();
    
    const name = document.getElementById('userName').value;
    const email = document.getElementById('userEmail').value;
    const role = document.getElementById('userRole').value;
    const platform = document.getElementById('userPlatform').value;
    const password = document.getElementById('userPassword').value;
    
    // Check if email already exists
    const existingUser = getUsers().find(u => u.email === email);
    if (existingUser) {
        showNotification('A user with this email already exists.', 'error');
        return;
    }
    
    const newUser = {
        id: Math.max(...getUsers().map(u => u.id)) + 1,
        name,
        email,
        role,
        platform,
        active: true,
        created: new Date().toISOString().split('T')[0],
        passwordChangeRequired: true
    };
    
    // In a real application, password would be hashed server-side
    mockData.users.push(newUser);
    saveToLocalStorage();
    
    showNotification('User added successfully!', 'success');
    manageUsers(); // Refresh the user list
}

// Edit user
function editUser(userId) {
    const user = getUserById(userId);
    if (!user) {
        showNotification('User not found.', 'error');
        return;
    }
    
    const modalBody = document.getElementById('modalBody');
    
    modalBody.innerHTML = `
        <h2>Edit User</h2>
        <form id="editUserForm" onsubmit="updateUser(event, ${userId})">
            <div class="form-row">
                <div class="form-group">
                    <label for="editUserName">Full Name:</label>
                    <input type="text" id="editUserName" required value="${user.name}">
                </div>
                <div class="form-group">
                    <label for="editUserEmail">Email:</label>
                    <input type="email" id="editUserEmail" required value="${user.email}">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="editUserRole">Role:</label>
                    <select id="editUserRole" required>
                        <option value="admin" ${user.role === 'admin' ? 'selected' : ''}>Admin - Full Access</option>
                        <option value="readwrite" ${user.role === 'readwrite' ? 'selected' : ''}>Read & Write - Needs Approval</option>
                        <option value="read" ${user.role === 'read' ? 'selected' : ''}>Read Only - View Access</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="editUserPlatform">Platform Tier:</label>
                    <select id="editUserPlatform" required>
                        <option value="silver" ${user.platform === 'silver' ? 'selected' : ''}>Silver</option>
                        <option value="gold" ${user.platform === 'gold' ? 'selected' : ''}>Gold</option>
                        <option value="platinum" ${user.platform === 'platinum' ? 'selected' : ''}>Platinum</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label for="editUserActive">Status:</label>
                <select id="editUserActive">
                    <option value="true" ${user.active ? 'selected' : ''}>Active</option>
                    <option value="false" ${!user.active ? 'selected' : ''}>Inactive</option>
                </select>
            </div>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" id="resetPassword"> 
                    Reset password (user will be required to change on next login)
                </label>
            </div>
            
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="manageUsers()">Back</button>
                <button type="submit" class="btn btn-primary">Update User</button>
            </div>
        </form>
    `;
    
    showModal();
}

// Update user
function updateUser(event, userId) {
    event.preventDefault();
    
    const name = document.getElementById('editUserName').value;
    const email = document.getElementById('editUserEmail').value;
    const role = document.getElementById('editUserRole').value;
    const platform = document.getElementById('editUserPlatform').value;
    const active = document.getElementById('editUserActive').value === 'true';
    const resetPassword = document.getElementById('resetPassword').checked;
    
    // Check if email already exists (excluding current user)
    const existingUser = getUsers().find(u => u.email === email && u.id !== userId);
    if (existingUser) {
        showNotification('A user with this email already exists.', 'error');
        return;
    }
    
    const userIndex = mockData.users.findIndex(u => u.id === userId);
    if (userIndex !== -1) {
        mockData.users[userIndex] = {
            ...mockData.users[userIndex],
            name,
            email,
            role,
            platform,
            active,
            passwordChangeRequired: resetPassword || mockData.users[userIndex].passwordChangeRequired
        };
        
        // If updating current user, update the session
        if (userId === getCurrentUser().id) {
            mockData.currentUser = mockData.users[userIndex];
            auth.currentUser = mockData.users[userIndex];
            auth.updateUI();
        }
        
        saveToLocalStorage();
        showNotification('User updated successfully!', 'success');
        manageUsers(); // Refresh the user list
    } else {
        showNotification('Error updating user.', 'error');
    }
}

// Toggle user status
function toggleUserStatus(userId) {
    const user = getUserById(userId);
    if (!user) {
        showNotification('User not found.', 'error');
        return;
    }
    
    if (userId === getCurrentUser().id) {
        showNotification('You cannot deactivate your own account.', 'error');
        return;
    }
    
    const action = user.active ? 'deactivate' : 'activate';
    const confirmMessage = `Are you sure you want to ${action} ${user.name}?`;
    
    if (confirm(confirmMessage)) {
        const userIndex = mockData.users.findIndex(u => u.id === userId);
        if (userIndex !== -1) {
            mockData.users[userIndex].active = !mockData.users[userIndex].active;
            saveToLocalStorage();
            showNotification(`User ${action}d successfully!`, 'success');
            manageUsers(); // Refresh the user list
        }
    }
}

// Role and platform management
function manageRolesAndPlatforms() {
    if (!auth.isAdmin()) {
        showNotification('Access denied. Admin privileges required.', 'error');
        return;
    }
    
    const modalBody = document.getElementById('modalBody');
    
    modalBody.innerHTML = `
        <h2>Roles & Platforms Configuration</h2>
        
        <div class="config-section">
            <h3>User Roles</h3>
            <div class="role-config">
                ${Object.entries(USER_ROLES).map(([key, role]) => `
                    <div class="config-item">
                        <h4>${role.name}</h4>
                        <p>Permissions: ${role.permissions.map(p => formatPermission(p)).join(', ')}</p>
                    </div>
                `).join('')}
            </div>
        </div>
        
        <div class="config-section">
            <h3>Platform Tiers</h3>
            <div class="platform-config">
                ${Object.entries(PLATFORMS).map(([key, platform]) => `
                    <div class="config-item">
                        <h4>${platform.name}</h4>
                        <p><strong>Features:</strong> ${platform.features.slice(0, 3).join(', ')}${platform.features.length > 3 ? '...' : ''}</p>
                        <p><strong>Limits:</strong> ${platform.maxUsers === -1 ? 'Unlimited' : platform.maxUsers} users, ${platform.maxPOs === -1 ? 'Unlimited' : platform.maxPOs} POs</p>
                    </div>
                `).join('')}
            </div>
        </div>
        
        <div class="form-actions">
            <button type="button" class="btn btn-secondary" onclick="closeModal()">Close</button>
        </div>
    `;
    
    showModal();
}

// User activity log (placeholder)
function viewUserActivity(userId) {
    const user = getUserById(userId);
    if (!user) {
        showNotification('User not found.', 'error');
        return;
    }
    
    // In a real application, this would fetch actual activity logs
    const mockActivity = [
        { action: 'Login', timestamp: '2024-03-01 09:00:00', details: 'Successful login' },
        { action: 'Created PO', timestamp: '2024-03-01 10:30:00', details: 'PO-2024-001 for Acme Corp' },
        { action: 'Updated Customer', timestamp: '2024-03-01 14:15:00', details: 'Modified TechStart Inc details' },
        { action: 'Logout', timestamp: '2024-03-01 17:00:00', details: 'Session ended' }
    ];
    
    const modalBody = document.getElementById('modalBody');
    
    modalBody.innerHTML = `
        <h2>User Activity Log - ${user.name}</h2>
        
        <div class="activity-log">
            <table class="po-table">
                <thead>
                    <tr>
                        <th>Action</th>
                        <th>Timestamp</th>
                        <th>Details</th>
                    </tr>
                </thead>
                <tbody>
                    ${mockActivity.map(activity => `
                        <tr>
                            <td>${activity.action}</td>
                            <td>${activity.timestamp}</td>
                            <td>${activity.details}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
        
        <div class="form-actions">
            <button type="button" class="btn btn-secondary" onclick="manageUsers()">Back</button>
        </div>
    `;
    
    showModal();
}
