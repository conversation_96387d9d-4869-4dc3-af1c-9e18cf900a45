// Authentication and authorization system

class AuthSystem {
    constructor() {
        this.currentUser = null;
        this.sessionTimeout = 30 * 60 * 1000; // 30 minutes
        this.sessionTimer = null;
    }

    // Initialize authentication system
    init() {
        this.loadSession();
        this.setupSessionTimeout();
        this.updateUI();
    }

    // Check if user has specific permission
    hasPermission(permission) {
        if (!this.currentUser) return false;
        const role = USER_ROLES[this.currentUser.role];
        return role && role.permissions.includes(permission);
    }

    // Check if user can access admin features
    isAdmin() {
        return this.hasPermission('admin');
    }

    // Check if user can write/edit
    canWrite() {
        return this.hasPermission('write') || this.hasPermission('admin');
    }

    // Check if user can approve
    canApprove() {
        return this.hasPermission('approve');
    }

    // Login function (mock implementation)
    login(email, password) {
        // In a real application, this would make an API call
        const user = mockData.users.find(u => u.email === email && u.active);
        
        if (user) {
            // Mock password validation (in real app, this would be handled server-side)
            if (this.validatePassword(email, password)) {
                this.currentUser = user;
                mockData.currentUser = user;
                this.saveSession();
                this.updateUI();
                this.resetSessionTimeout();
                return { success: true, user: user };
            }
        }
        
        return { success: false, message: 'Invalid credentials' };
    }

    // Mock password validation
    validatePassword(email, password) {
        // Mock validation - in real app, this would be server-side
        const validCredentials = {
            '<EMAIL>': 'admin123',
            '<EMAIL>': 'jane123',
            '<EMAIL>': 'bob123'
        };
        
        return validCredentials[email] === password;
    }

    // Logout function
    logout() {
        this.currentUser = null;
        mockData.currentUser = null;
        this.clearSession();
        this.clearSessionTimeout();
        this.redirectToLogin();
    }

    // Save session to localStorage
    saveSession() {
        if (this.currentUser) {
            const session = {
                user: this.currentUser,
                timestamp: Date.now()
            };
            localStorage.setItem('companyPortalSession', JSON.stringify(session));
        }
    }

    // Load session from localStorage
    loadSession() {
        const session = localStorage.getItem('companyPortalSession');
        if (session) {
            try {
                const parsed = JSON.parse(session);
                const now = Date.now();
                
                // Check if session is still valid (not expired)
                if (now - parsed.timestamp < this.sessionTimeout) {
                    this.currentUser = parsed.user;
                    mockData.currentUser = parsed.user;
                    return true;
                }
            } catch (e) {
                console.error('Error loading session:', e);
            }
        }
        
        this.clearSession();
        return false;
    }

    // Clear session
    clearSession() {
        localStorage.removeItem('companyPortalSession');
    }

    // Setup session timeout
    setupSessionTimeout() {
        this.resetSessionTimeout();
    }

    // Reset session timeout
    resetSessionTimeout() {
        if (this.sessionTimer) {
            clearTimeout(this.sessionTimer);
        }
        
        if (this.currentUser) {
            this.sessionTimer = setTimeout(() => {
                this.showSessionExpiredMessage();
                this.logout();
            }, this.sessionTimeout);
        }
    }

    // Clear session timeout
    clearSessionTimeout() {
        if (this.sessionTimer) {
            clearTimeout(this.sessionTimer);
            this.sessionTimer = null;
        }
    }

    // Show session expired message
    showSessionExpiredMessage() {
        showNotification('Session expired. Please login again.', 'warning');
    }

    // Update UI based on authentication state
    updateUI() {
        const body = document.body;
        
        if (this.currentUser) {
            // Update user info in navbar
            const userRoleElement = document.getElementById('userRole');
            const platformBadgeElement = document.getElementById('platformBadge');
            
            if (userRoleElement) {
                userRoleElement.textContent = USER_ROLES[this.currentUser.role].name;
            }
            
            if (platformBadgeElement) {
                platformBadgeElement.textContent = PLATFORMS[this.currentUser.platform].name;
                platformBadgeElement.className = `platform-badge ${this.currentUser.platform}`;
            }
            
            // Show/hide admin features
            if (this.isAdmin()) {
                body.classList.add('admin');
            } else {
                body.classList.remove('admin');
            }
            
            // Update button visibility based on permissions
            this.updateButtonVisibility();
            
        } else {
            // User not logged in
            body.classList.remove('admin');
            this.redirectToLogin();
        }
    }

    // Update button visibility based on user permissions
    updateButtonVisibility() {
        const createPOBtn = document.getElementById('createPOBtn');
        const addCustomerBtn = document.getElementById('addCustomerBtn');
        
        if (createPOBtn) {
            createPOBtn.style.display = this.canWrite() ? 'inline-block' : 'none';
        }
        
        if (addCustomerBtn) {
            addCustomerBtn.style.display = this.canWrite() ? 'inline-block' : 'none';
        }
        
        // Update table action buttons
        this.updateTableActions();
    }

    // Update table action buttons based on permissions
    updateTableActions() {
        const actionButtons = document.querySelectorAll('.action-btn');
        
        actionButtons.forEach(btn => {
            const action = btn.classList.contains('action-edit') ? 'edit' :
                          btn.classList.contains('action-delete') ? 'delete' :
                          btn.classList.contains('action-approve') ? 'approve' :
                          btn.classList.contains('action-reject') ? 'approve' : 'view';
            
            switch (action) {
                case 'edit':
                case 'delete':
                    btn.style.display = this.canWrite() ? 'inline-block' : 'none';
                    break;
                case 'approve':
                    btn.style.display = this.canApprove() ? 'inline-block' : 'none';
                    break;
                default:
                    btn.style.display = 'inline-block';
            }
        });
    }

    // Redirect to login page
    redirectToLogin() {
        // In a real application, this would redirect to a login page
        // For this demo, we'll show a login modal
        this.showLoginModal();
    }

    // Show login modal
    showLoginModal() {
        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = `
            <h2>Login to Company Portal</h2>
            <form id="loginForm" onsubmit="handleLogin(event)">
                <div class="form-group">
                    <label for="loginEmail">Email:</label>
                    <input type="email" id="loginEmail" required value="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="loginPassword">Password:</label>
                    <input type="password" id="loginPassword" required value="admin123">
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">Login</button>
                </div>
            </form>
            <div style="margin-top: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 5px;">
                <h4>Demo Credentials:</h4>
                <p><strong>Admin:</strong> <EMAIL> / admin123</p>
                <p><strong>Manager:</strong> <EMAIL> / jane123</p>
                <p><strong>Viewer:</strong> <EMAIL> / bob123</p>
            </div>
        `;
        
        showModal();
    }

    // Get current user
    getCurrentUser() {
        return this.currentUser;
    }

    // Check if user is logged in
    isLoggedIn() {
        return this.currentUser !== null;
    }

    // Extend session (reset timeout)
    extendSession() {
        if (this.currentUser) {
            this.saveSession();
            this.resetSessionTimeout();
        }
    }
}

// Global auth instance
const auth = new AuthSystem();

// Handle login form submission
function handleLogin(event) {
    event.preventDefault();
    
    const email = document.getElementById('loginEmail').value;
    const password = document.getElementById('loginPassword').value;
    
    const result = auth.login(email, password);
    
    if (result.success) {
        closeModal();
        showNotification('Login successful!', 'success');
        // Refresh the page content
        if (typeof initializeDashboard === 'function') {
            initializeDashboard();
        }
    } else {
        showNotification(result.message, 'error');
    }
}

// Logout function (called from UI)
function logout() {
    auth.logout();
}

// Initialize auth system when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    auth.init();
    
    // Extend session on user activity
    document.addEventListener('click', () => auth.extendSession());
    document.addEventListener('keypress', () => auth.extendSession());
});

// Export for use in other modules
window.auth = auth;
