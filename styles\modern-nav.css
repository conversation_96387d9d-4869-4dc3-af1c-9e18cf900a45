/* Modern Navigation Styles */

/* CSS Variables */
:root {
    /* HCL Software Brand Colors */
    --primary-color: #0066cc;
    --primary-dark: #004499;
    --primary-light: #3385d6;
    --secondary-color: #00a86b;
    --accent-color: #ff6b35;

    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* Status Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;

    /* Typography */
    --font-sans: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', monospace;

    /* Navigation specific */
    --nav-height: 70px;
    --breadcrumb-height: 50px;
}

/* Reset and Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-sans);
    background-color: var(--gray-50);
    color: var(--gray-800);
    line-height: 1.6;
    font-size: 14px;
    overflow-x: hidden;
}

#app {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Top Navigation */
.top-nav {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    position: sticky;
    top: 0;
    z-index: 1000;
    height: var(--nav-height);
    box-shadow: var(--shadow-sm);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-6);
}

/* Brand Section */
.nav-brand {
    display: flex;
    align-items: center;
    flex-shrink: 0;
}

.company-logo {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.company-logo i {
    font-size: 1.75rem;
    color: var(--primary-color);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.company-info h1 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.platform-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius);
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    background: var(--gray-100);
    color: var(--gray-600);
}

.platform-badge.gold {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: var(--white);
}

.platform-badge.platinum {
    background: linear-gradient(135deg, #9ca3af, #6b7280);
    color: var(--white);
}

.platform-badge.silver {
    background: linear-gradient(135deg, #e5e7eb, #d1d5db);
    color: var(--gray-700);
}

/* Navigation Menu */
.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    flex: 1;
    justify-content: center;
    max-width: 600px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    color: var(--gray-600);
    text-decoration: none;
    border-radius: var(--radius);
    transition: all 0.2s ease;
    font-weight: 500;
    white-space: nowrap;
    position: relative;
}

.nav-item:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

.nav-item.active {
    background: var(--primary-color);
    color: var(--white);
}

.nav-item.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid var(--primary-color);
}

.nav-item i {
    font-size: 1rem;
    width: 16px;
    text-align: center;
}

.nav-item span {
    font-size: 0.9rem;
}

/* Navigation Actions */
.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    flex-shrink: 0;
}

/* Search Container */
.search-container {
    position: relative;
}

.search-toggle {
    background: none;
    border: none;
    padding: var(--space-3);
    border-radius: var(--radius);
    cursor: pointer;
    color: var(--gray-600);
    transition: all 0.2s ease;
}

.search-toggle:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

.search-box {
    position: absolute;
    top: 100%;
    right: 0;
    width: 300px;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius);
    box-shadow: var(--shadow-lg);
    padding: var(--space-3);
    display: none;
    z-index: 1001;
}

.search-box.active {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.search-box input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 0.9rem;
    color: var(--gray-700);
}

.search-box input::placeholder {
    color: var(--gray-400);
}

.search-close {
    background: none;
    border: none;
    padding: var(--space-1);
    cursor: pointer;
    color: var(--gray-400);
    border-radius: var(--radius-sm);
}

.search-close:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

/* Search Results Dropdown */
.search-results-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 400px;
    max-width: 90vw;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    z-index: 1002;
    max-height: 500px;
    overflow: hidden;
    display: block;
    animation: dropdownFadeIn 0.2s ease;
}

.search-results-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-3) var(--space-4);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.search-results-header span {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--gray-900);
}

.search-clear {
    background: none;
    border: none;
    padding: var(--space-1);
    border-radius: var(--radius-sm);
    cursor: pointer;
    color: var(--gray-400);
    transition: all 0.2s ease;
}

.search-clear:hover {
    background: var(--gray-200);
    color: var(--gray-600);
}

.search-results-list {
    max-height: 350px;
    overflow-y: auto;
    padding: var(--space-2);
}

.search-result-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3);
    border-radius: var(--radius);
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.search-result-item:hover {
    background: var(--gray-50);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.search-result-icon {
    width: 40px;
    height: 40px;
    background: var(--gray-100);
    border-radius: var(--radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-600);
    flex-shrink: 0;
}

.search-result-item:hover .search-result-icon {
    background: var(--primary-color);
    color: var(--white);
}

.search-result-content {
    flex: 1;
    min-width: 0;
}

.search-result-title {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 0.9rem;
    margin-bottom: var(--space-1);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.search-result-title mark {
    background: var(--warning);
    color: var(--gray-900);
    padding: 1px 2px;
    border-radius: 2px;
    font-weight: 700;
}

.search-result-subtitle {
    font-size: 0.8rem;
    color: var(--gray-600);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.search-result-arrow {
    color: var(--gray-400);
    font-size: 0.8rem;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.search-result-item:hover .search-result-arrow {
    color: var(--primary-color);
    transform: translateX(2px);
}

.search-no-results {
    text-align: center;
    padding: var(--space-8) var(--space-4);
    color: var(--gray-500);
}

.search-no-results i {
    font-size: 2rem;
    margin-bottom: var(--space-3);
    color: var(--gray-400);
}

.search-no-results p {
    font-weight: 600;
    margin-bottom: var(--space-2);
    color: var(--gray-700);
}

.search-no-results small {
    color: var(--gray-500);
}

.search-results-footer {
    padding: var(--space-3);
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.search-view-all {
    width: 100%;
    background: none;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius);
    cursor: pointer;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.search-view-all:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* Search Highlight Animation */
.search-highlight {
    background: linear-gradient(90deg,
        rgba(59, 130, 246, 0.1),
        rgba(59, 130, 246, 0.2),
        rgba(59, 130, 246, 0.1)
    );
    border: 2px solid var(--primary-color);
    border-radius: var(--radius);
    animation: searchHighlight 2s ease-in-out;
}

@keyframes searchHighlight {
    0% {
        background: rgba(59, 130, 246, 0.3);
        transform: scale(1.02);
    }
    50% {
        background: rgba(59, 130, 246, 0.1);
        transform: scale(1);
    }
    100% {
        background: rgba(59, 130, 246, 0.05);
        transform: scale(1);
    }
}

@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Action Buttons */
.nav-action-btn {
    position: relative;
    background: none;
    border: none;
    padding: var(--space-3);
    border-radius: var(--radius);
    cursor: pointer;
    color: var(--gray-600);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-action-btn:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

.notification-badge {
    position: absolute;
    top: 6px;
    right: 6px;
    background: var(--error);
    color: var(--white);
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    font-weight: 600;
}

/* User Menu Button */
.user-menu-btn {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-2) var(--space-3);
    background: none;
    border: none;
    border-radius: var(--radius);
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--gray-700);
}

.user-menu-btn:hover {
    background: var(--gray-100);
}

.user-avatar {
    width: 36px;
    height: 36px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1rem;
    flex-shrink: 0;
}

.user-avatar.large {
    width: 48px;
    height: 48px;
    font-size: 1.2rem;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
}

.user-name {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 0.9rem;
    line-height: 1.2;
}

.user-role {
    font-size: 0.75rem;
    color: var(--gray-500);
    line-height: 1.2;
}

.user-email {
    font-size: 0.8rem;
    color: var(--gray-500);
    line-height: 1.2;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    padding: var(--space-3);
    border-radius: var(--radius);
    cursor: pointer;
    color: var(--gray-600);
    transition: all 0.2s ease;
}

.mobile-menu-toggle:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

/* Dropdown Menus */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    min-width: 250px;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--space-2);
    display: none;
    z-index: 1001;
    max-height: 400px;
    overflow-y: auto;
}

.dropdown-menu.show {
    display: block;
    animation: dropdownFadeIn 0.2s ease;
}

.dropdown-menu-right {
    right: 0;
    left: auto;
}

.dropdown-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-3) var(--space-2);
    border-bottom: 1px solid var(--gray-200);
    margin-bottom: var(--space-2);
}

.dropdown-header h4 {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-2);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--radius);
    transition: all 0.2s ease;
    font-size: 0.9rem;
    cursor: pointer;
}

.dropdown-item:hover {
    background: var(--gray-100);
    color: var(--gray-900);
}

.dropdown-item i {
    width: 16px;
    text-align: center;
    color: var(--gray-500);
}

.dropdown-item.text-danger {
    color: var(--error);
}

.dropdown-item.text-danger:hover {
    background: var(--error);
    color: var(--white);
}

.dropdown-divider {
    height: 1px;
    background: var(--gray-200);
    margin: var(--space-2) 0;
}

.dropdown-footer {
    padding: var(--space-2);
    border-top: 1px solid var(--gray-200);
    margin-top: var(--space-2);
}

.dropdown-footer a {
    display: block;
    text-align: center;
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.85rem;
    font-weight: 500;
    padding: var(--space-2);
    border-radius: var(--radius);
    transition: all 0.2s ease;
}

.dropdown-footer a:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* Mobile Navigation */
.mobile-nav {
    position: fixed;
    top: 0;
    right: -100%;
    width: 320px;
    height: 100vh;
    background: var(--white);
    box-shadow: var(--shadow-xl);
    z-index: 1002;
    transition: right 0.3s ease;
    display: flex;
    flex-direction: column;
}

.mobile-nav.show {
    right: 0;
}

.mobile-nav-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4);
    border-bottom: 1px solid var(--gray-200);
}

.mobile-nav-close {
    background: none;
    border: none;
    padding: var(--space-2);
    border-radius: var(--radius);
    cursor: pointer;
    color: var(--gray-600);
}

.mobile-nav-close:hover {
    background: var(--gray-100);
    color: var(--gray-900);
}

.mobile-nav-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--space-4);
}

.mobile-nav-section {
    margin-bottom: var(--space-6);
}

.mobile-nav-section h4 {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--space-3);
}

.mobile-nav-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--radius);
    transition: all 0.2s ease;
    margin-bottom: var(--space-1);
}

.mobile-nav-item:hover {
    background: var(--gray-100);
    color: var(--gray-900);
}

.mobile-nav-item.active {
    background: var(--primary-color);
    color: var(--white);
}

.mobile-nav-item i {
    width: 20px;
    text-align: center;
}

.mobile-nav-footer {
    padding: var(--space-4);
    border-top: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.mobile-nav-footer .user-info {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    flex: 1;
}

.mobile-nav-footer .logout-btn {
    background: var(--gray-100);
    border: none;
    padding: var(--space-2);
    border-radius: var(--radius);
    cursor: pointer;
    color: var(--gray-600);
    transition: all 0.2s ease;
}

.mobile-nav-footer .logout-btn:hover {
    background: var(--error);
    color: var(--white);
}

/* Breadcrumb Navigation */
.breadcrumb-nav {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    height: var(--breadcrumb-height);
    display: flex;
    align-items: center;
}

.breadcrumb-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-6);
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    color: var(--gray-600);
    text-decoration: none;
    font-size: 0.85rem;
    transition: color 0.2s ease;
}

.breadcrumb-item:hover {
    color: var(--primary-color);
}

.breadcrumb-item.active {
    color: var(--gray-900);
    font-weight: 500;
}

.breadcrumb-separator {
    color: var(--gray-400);
    font-size: 0.8rem;
}

.page-actions {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

/* Main Content */
.main-content {
    flex: 1;
    padding: var(--space-6);
    margin-top: 0;
    margin-left: 0;
}

/* Navigation Overlay */
.nav-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1001;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.nav-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Utility Classes */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    border: none;
    border-radius: var(--radius);
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--primary-dark);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* Text Colors */
.text-primary { color: var(--primary-color); }
.text-success { color: var(--success); }
.text-warning { color: var(--warning); }
.text-danger { color: var(--error); }
.text-info { color: var(--info); }

/* Role-based visibility */
.admin-only {
    display: none;
}

body.admin .admin-only {
    display: flex;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .nav-container {
        padding: 0 var(--space-4);
    }

    .nav-menu {
        gap: var(--space-1);
    }

    .nav-item {
        padding: var(--space-2) var(--space-3);
    }

    .nav-item span {
        display: none;
    }

    .nav-item i {
        font-size: 1.1rem;
    }

    .search-box {
        width: 250px;
    }

    .search-results-dropdown {
        width: 350px;
    }
}

@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }

    .nav-menu {
        display: none;
    }

    .search-container {
        display: none;
    }

    .nav-actions .dropdown:not(:last-child) {
        display: none;
    }

    .user-info {
        display: none;
    }

    .user-menu-btn i.fa-chevron-down {
        display: none;
    }

    .breadcrumb-container {
        padding: 0 var(--space-4);
    }

    .main-content {
        padding: var(--space-4);
    }

    .search-results-dropdown {
        width: 300px;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0 var(--space-3);
    }

    .company-info h1 {
        font-size: 1.1rem;
    }

    .platform-badge {
        font-size: 0.6rem;
        padding: 2px var(--space-1);
    }

    .breadcrumb-container {
        padding: 0 var(--space-3);
    }

    .main-content {
        padding: var(--space-3);
    }

    .mobile-nav {
        width: 100%;
        right: -100%;
    }

    .search-results-dropdown {
        width: 280px;
        max-width: 95vw;
    }

    .dropdown-menu {
        min-width: 200px;
    }
}

/* Notification styles for search feedback */
.notification-container {
    position: fixed;
    top: var(--nav-height);
    right: var(--space-4);
    z-index: 1003;
    pointer-events: none;
}

.notification {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius);
    box-shadow: var(--shadow-lg);
    padding: var(--space-3) var(--space-4);
    margin-bottom: var(--space-2);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    min-width: 300px;
    pointer-events: auto;
    animation: slideInRight 0.3s ease;
}

.notification.success {
    border-left: 4px solid var(--success);
}

.notification.warning {
    border-left: 4px solid var(--warning);
}

.notification.error {
    border-left: 4px solid var(--error);
}

.notification.info {
    border-left: 4px solid var(--info);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Modal styles for search results */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1004;
    display: none;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    animation: modalFadeIn 0.3s ease;
}

.modal-header {
    padding: var(--space-4);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-close {
    background: none;
    border: none;
    padding: var(--space-2);
    border-radius: var(--radius);
    cursor: pointer;
    color: var(--gray-600);
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-900);
}

.modal-body {
    padding: var(--space-4);
    overflow-y: auto;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Content Sections */
.content-section {
    display: none;
    animation: fadeIn 0.3s ease;
}

.content-section.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dashboard-header,
.section-header {
    margin-bottom: var(--space-6);
}

.dashboard-header h2,
.section-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.dashboard-header p,
.section-header p {
    color: var(--gray-600);
    font-size: 0.95rem;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-6);
}

.dashboard-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease;
}

.dashboard-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.dashboard-card h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-3);
}

.dashboard-card p {
    color: var(--gray-600);
    font-size: 0.9rem;
    line-height: 1.5;
}
