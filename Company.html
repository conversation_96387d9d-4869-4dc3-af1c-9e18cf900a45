<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HCL Software Portal - Dashboard</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Chart.js for graphs -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div id="app">
        <!-- Mobile Menu Toggle -->
        <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
            <i class="fas fa-bars"></i>
        </button>

        <!-- Sidebar Navigation -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="company-logo">
                    <i class="fas fa-cube"></i>
                    <div class="company-info">
                        <h1>HCL Software</h1>
                        <span class="platform-badge" id="platformBadge">Silver</span>
                    </div>
                </div>
            </div>

            <nav class="sidebar-nav">
                <a href="#dashboard" class="nav-link active" onclick="showSection('dashboard')">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="#purchase-orders" class="nav-link" onclick="showSection('purchase-orders')">
                    <i class="fas fa-shopping-cart"></i>
                    <span>Purchase Orders</span>
                </a>
                <a href="#customers" class="nav-link" onclick="showSection('customers')">
                    <i class="fas fa-users"></i>
                    <span>Customers</span>
                </a>
                <a href="#renewals" class="nav-link" onclick="showSection('renewals')">
                    <i class="fas fa-sync-alt"></i>
                    <span>Renewals</span>
                </a>
                <a href="#onboarding" class="nav-link" onclick="showSection('onboarding')">
                    <i class="fas fa-rocket"></i>
                    <span>On Boarding</span>
                </a>
                <a href="#professional-services" class="nav-link" onclick="showSection('professional-services')">
                    <i class="fas fa-briefcase"></i>
                    <span>Professional Services</span>
                </a>
                <a href="#admin" class="nav-link admin-only" onclick="showSection('admin')">
                    <i class="fas fa-cog"></i>
                    <span>Admin Panel</span>
                </a>
            </nav>

            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <span class="user-name" id="userName">John Admin</span>
                        <span class="user-role" id="userRole">Admin</span>
                    </div>
                </div>
                <button class="logout-btn" onclick="logout()" title="Logout">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Top Header -->
            <header class="top-header">
                <div class="header-left">
                    <h2 id="pageTitle">Dashboard</h2>
                    <p id="pageSubtitle">Welcome to your HCL Software portal</p>
                </div>
                <div class="header-right">
                    <div class="header-actions">
                        <button class="header-btn" onclick="showNotifications()" title="Notifications">
                            <i class="fas fa-bell"></i>
                            <span class="notification-badge">3</span>
                        </button>
                        <button class="header-btn" onclick="showHelp()" title="Help">
                            <i class="fas fa-question-circle"></i>
                        </button>
                    </div>
                </div>
            </header>

            <!-- Dashboard Section -->
            <section id="dashboard" class="content-section active">
                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon pending">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3>Pending Approvals</h3>
                            <span class="stat-number" id="pendingPOs">12</span>
                            <span class="stat-change positive" id="pendingAmount">$0 pending approval</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon customers">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3>Active Customers</h3>
                            <span class="stat-number" id="activeCustomers">45</span>
                            <span class="stat-change positive">+5 this month</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon orders">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-content">
                            <h3>Monthly Orders</h3>
                            <span class="stat-number" id="monthlyOrders">28</span>
                            <span class="stat-change positive">+12% vs last month</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon revenue">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-content">
                            <h3>Total Revenue</h3>
                            <span class="stat-number" id="totalRevenue">$125K</span>
                            <span class="stat-change positive">+8% this quarter</span>
                        </div>
                    </div>
                </div>

                <!-- Purchase Order Timeline Cards -->
                <div class="timeline-section">
                    <h3 class="timeline-section-title">
                        <i class="fas fa-chart-line"></i>
                        Purchase Order Timeline
                    </h3>

                    <div class="timeline-grid">
                        <!-- Today Card -->
                        <div class="timeline-card today-card">
                            <div class="timeline-header">
                                <div class="timeline-icon today">
                                    <i class="fas fa-calendar-day"></i>
                                </div>
                                <div class="timeline-info">
                                    <h4 class="timeline-title">Today</h4>
                                    <div class="timeline-subtitle">Purchase Orders Received</div>
                                </div>
                            </div>
                            <div class="timeline-stats">
                                <div class="timeline-number" id="todayPOs">0</div>
                                <div class="timeline-target">Target: <span id="todayTarget">5</span></div>
                            </div>
                            <div class="timeline-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill today-progress" id="todayProgressBar" style="width: 0%"></div>
                                </div>
                                <div class="progress-label">
                                    <span id="todayPercentage">0%</span> of target achieved
                                </div>
                            </div>
                        </div>

                        <!-- Weekly Card -->
                        <div class="timeline-card weekly-card">
                            <div class="timeline-header">
                                <div class="timeline-icon weekly">
                                    <i class="fas fa-calendar-week"></i>
                                </div>
                                <div class="timeline-info">
                                    <h4 class="timeline-title">This Week</h4>
                                    <div class="timeline-subtitle">Purchase Orders Received</div>
                                </div>
                            </div>
                            <div class="timeline-stats">
                                <div class="timeline-number" id="weeklyPOs">0</div>
                                <div class="timeline-target">Target: <span id="weeklyTarget">25</span></div>
                            </div>
                            <div class="timeline-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill weekly-progress" id="weeklyProgressBar" style="width: 0%"></div>
                                </div>
                                <div class="progress-label">
                                    <span id="weeklyPercentage">0%</span> of target achieved
                                </div>
                            </div>
                        </div>

                        <!-- MTD Card -->
                        <div class="timeline-card mtd-card">
                            <div class="timeline-header">
                                <div class="timeline-icon mtd">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div class="timeline-info">
                                    <h4 class="timeline-title">MTD</h4>
                                    <div class="timeline-subtitle">Month to Date Orders</div>
                                </div>
                            </div>
                            <div class="timeline-stats">
                                <div class="timeline-number" id="mtdPOs">0</div>
                                <div class="timeline-target">Target: <span id="mtdTarget">100</span></div>
                            </div>
                            <div class="timeline-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill mtd-progress" id="mtdProgressBar" style="width: 0%"></div>
                                </div>
                                <div class="progress-label">
                                    <span id="mtdPercentage">0%</span> of target achieved
                                </div>
                            </div>
                        </div>

                        <!-- YTD Card -->
                        <div class="timeline-card ytd-card">
                            <div class="timeline-header">
                                <div class="timeline-icon ytd">
                                    <i class="fas fa-calendar"></i>
                                </div>
                                <div class="timeline-info">
                                    <h4 class="timeline-title">YTD</h4>
                                    <div class="timeline-subtitle">Year to Date Orders</div>
                                </div>
                            </div>
                            <div class="timeline-stats">
                                <div class="timeline-number" id="ytdPOs">0</div>
                                <div class="timeline-target">Target: <span id="ytdTarget">1200</span></div>
                            </div>
                            <div class="timeline-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill ytd-progress" id="ytdProgressBar" style="width: 0%"></div>
                                </div>
                                <div class="progress-label">
                                    <span id="ytdPercentage">0%</span> of target achieved
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dashboard Grid -->
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3><i class="fas fa-chart-pie"></i> PO Status Overview</h3>
                            <button class="btn btn-outline" onclick="showSection('purchase-orders')">
                                <i class="fas fa-external-link-alt"></i> View Details
                            </button>
                        </div>
                        <div class="card-content">
                            <div class="chart-container" style="height: 250px;">
                                <canvas id="dashboardStatusChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3><i class="fas fa-list-alt"></i> Recent Purchase Orders</h3>
                            <button class="btn btn-outline" onclick="showSection('purchase-orders')">
                                <i class="fas fa-external-link-alt"></i> View All
                            </button>
                        </div>
                        <div class="card-content">
                            <div class="po-list" id="recentPOs">
                                <!-- Dynamic content -->
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3><i class="fas fa-star"></i> Platform Features</h3>
                            <span class="platform-tier" id="platformTier">Silver Tier</span>
                        </div>
                        <div class="card-content">
                            <div class="platform-features" id="platformFeatures">
                                <!-- Dynamic content based on platform tier -->
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3><i class="fas fa-chart-pie"></i> Customer Distribution</h3>
                        </div>
                        <div class="card-content">
                            <div class="customer-distribution" id="customerDistribution">
                                <!-- Dynamic content -->
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3><i class="fas fa-tasks"></i> Quick Actions</h3>
                        </div>
                        <div class="card-content">
                            <div class="quick-actions">
                                <button class="quick-action-btn" onclick="createNewPO()">
                                    <i class="fas fa-plus"></i>
                                    <span>New Purchase Order</span>
                                </button>
                                <button class="quick-action-btn" onclick="addCustomer()">
                                    <i class="fas fa-user-plus"></i>
                                    <span>Add Customer</span>
                                </button>
                                <button class="quick-action-btn admin-only" onclick="manageUsers()">
                                    <i class="fas fa-users-cog"></i>
                                    <span>Manage Users</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Purchase Orders Section -->
            <section id="purchase-orders" class="content-section">
                <div class="section-header">
                    <div class="section-title">
                        <h2><i class="fas fa-shopping-cart"></i> Purchase Orders</h2>
                        <p>Manage and track all purchase orders</p>
                    </div>
                    <button class="btn btn-primary" onclick="createNewPO()" id="createPOBtn">
                        <i class="fas fa-plus"></i> Create New PO
                    </button>
                </div>

                <!-- Purchase Order Analytics -->
                <div class="analytics-section">
                    <h3 class="analytics-section-title">
                        <i class="fas fa-chart-bar"></i>
                        Purchase Order Analytics
                    </h3>

                    <div class="analytics-grid">
                        <!-- Status Distribution Chart -->
                        <div class="analytics-card">
                            <div class="analytics-header">
                                <h4><i class="fas fa-chart-pie"></i> Status Distribution</h4>
                                <button class="btn btn-outline btn-sm" onclick="refreshCharts()">
                                    <i class="fas fa-sync-alt"></i> Refresh
                                </button>
                            </div>
                            <div class="chart-container">
                                <canvas id="statusChart" width="400" height="300"></canvas>
                            </div>
                        </div>

                        <!-- Monthly Trends Chart -->
                        <div class="analytics-card">
                            <div class="analytics-header">
                                <h4><i class="fas fa-chart-line"></i> Monthly Trends</h4>
                                <div class="chart-controls">
                                    <select id="trendPeriod" onchange="updateTrendChart()">
                                        <option value="6">Last 6 Months</option>
                                        <option value="12">Last 12 Months</option>
                                    </select>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="trendsChart" width="400" height="300"></canvas>
                            </div>
                        </div>

                        <!-- Customer Type Analysis -->
                        <div class="analytics-card">
                            <div class="analytics-header">
                                <h4><i class="fas fa-chart-bar"></i> Amount by Customer Type</h4>
                                <div class="chart-legend">
                                    <span class="legend-item"><span class="legend-color nn"></span> NN</span>
                                    <span class="legend-item"><span class="legend-color en"></span> EN</span>
                                    <span class="legend-item"><span class="legend-color ee"></span> EE</span>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="customerTypeChart" width="400" height="300"></canvas>
                            </div>
                        </div>

                        <!-- Top Customers Chart -->
                        <div class="analytics-card">
                            <div class="analytics-header">
                                <h4><i class="fas fa-trophy"></i> Top Customers by Value</h4>
                                <div class="chart-controls">
                                    <select id="topCustomersCount" onchange="updateTopCustomersChart()">
                                        <option value="5">Top 5</option>
                                        <option value="10">Top 10</option>
                                    </select>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="topCustomersChart" width="400" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters and Search -->
                <div class="filters-container">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="poSearch" placeholder="Search purchase orders..." onkeyup="searchPOs()">
                    </div>

                    <div class="filter-group">
                        <div class="filter-item">
                            <label><i class="fas fa-filter"></i> Status</label>
                            <select id="statusFilter" onchange="filterPOs()">
                                <option value="">All Status</option>
                                <option value="draft">📝 Draft</option>
                                <option value="pending">⏳ Pending Approval</option>
                                <option value="approved">✅ Approved</option>
                                <option value="rejected">❌ Rejected</option>
                            </select>
                        </div>

                        <div class="filter-item">
                            <label><i class="fas fa-users"></i> Customer Type</label>
                            <select id="customerFilter" onchange="filterPOs()">
                                <option value="">All Customers</option>
                                <option value="NN">🆕 NN Customers</option>
                                <option value="EN">🔄 EN Customers</option>
                                <option value="EE">🏢 EE Customers</option>
                            </select>
                        </div>

                        <button class="btn btn-outline" onclick="clearFilters()">
                            <i class="fas fa-times"></i> Clear
                        </button>
                    </div>
                </div>

                <!-- Purchase Orders Table -->
                <div class="table-container">
                    <div class="table-header">
                        <h3><i class="fas fa-list"></i> Purchase Orders List</h3>
                        <div class="table-actions">
                            <button class="btn btn-outline" onclick="exportPOs()">
                                <i class="fas fa-download"></i> Export
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-hashtag"></i> PO Number</th>
                                    <th><i class="fas fa-building"></i> Customer</th>
                                    <th><i class="fas fa-tag"></i> Type</th>
                                    <th><i class="fas fa-dollar-sign"></i> Amount</th>
                                    <th><i class="fas fa-info-circle"></i> Status</th>
                                    <th><i class="fas fa-calendar"></i> Created</th>
                                    <th><i class="fas fa-cogs"></i> Actions</th>
                                </tr>
                            </thead>
                            <tbody id="poTableBody">
                                <!-- Dynamic content -->
                            </tbody>
                        </table>
                    </div>

                    <div class="table-footer">
                        <div class="table-info">
                            <span id="tableInfo">Showing 0 of 0 purchase orders</span>
                        </div>
                        <div class="table-pagination">
                            <button class="btn btn-outline" onclick="previousPage()" id="prevBtn" disabled>
                                <i class="fas fa-chevron-left"></i> Previous
                            </button>
                            <span class="page-info" id="pageInfo">Page 1 of 1</span>
                            <button class="btn btn-outline" onclick="nextPage()" id="nextBtn" disabled>
                                Next <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Customers Section -->
            <section id="customers" class="content-section">
                <div class="section-header">
                    <div class="section-title">
                        <h2><i class="fas fa-users"></i> Customer Management</h2>
                        <p>Manage your customer relationships and data</p>
                    </div>
                    <button class="btn btn-primary" onclick="addCustomer()" id="addCustomerBtn">
                        <i class="fas fa-user-plus"></i> Add Customer
                    </button>
                </div>

                <!-- Customer Type Overview -->
                <div class="customer-overview">
                    <div class="customer-type-card" data-type="NN" onclick="filterCustomersByType('NN')">
                        <div class="card-icon nn">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="card-content">
                            <h3>NN Customers</h3>
                            <p>New Normal customers</p>
                            <div class="customer-count" id="nnCount">0</div>
                        </div>
                        <div class="card-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>

                    <div class="customer-type-card" data-type="EN" onclick="filterCustomersByType('EN')">
                        <div class="card-icon en">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="card-content">
                            <h3>EN Customers</h3>
                            <p>Existing Normal customers</p>
                            <div class="customer-count" id="enCount">0</div>
                        </div>
                        <div class="card-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>

                    <div class="customer-type-card" data-type="EE" onclick="filterCustomersByType('EE')">
                        <div class="card-icon ee">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="card-content">
                            <h3>EE Customers</h3>
                            <p>Existing Enterprise customers</p>
                            <div class="customer-count" id="eeCount">0</div>
                            <div class="renewal-types">
                                <span class="renewal-badge"><i class="fas fa-key"></i> License</span>
                                <span class="renewal-badge"><i class="fas fa-headset"></i> Support</span>
                                <span class="renewal-badge"><i class="fas fa-edit"></i> CR</span>
                            </div>
                        </div>
                        <div class="card-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>

                <!-- Customer Search and Filters -->
                <div class="customer-controls">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="customerSearch" placeholder="Search customers..." onkeyup="searchCustomers()">
                    </div>

                    <div class="filter-group">
                        <button class="filter-btn active" onclick="filterCustomersByType('')" data-type="">
                            <i class="fas fa-users"></i> All Customers
                        </button>
                        <button class="filter-btn" onclick="filterCustomersByType('NN')" data-type="NN">
                            <i class="fas fa-user-plus"></i> NN
                        </button>
                        <button class="filter-btn" onclick="filterCustomersByType('EN')" data-type="EN">
                            <i class="fas fa-user-check"></i> EN
                        </button>
                        <button class="filter-btn" onclick="filterCustomersByType('EE')" data-type="EE">
                            <i class="fas fa-building"></i> EE
                        </button>
                    </div>
                </div>

                <!-- Customer List -->
                <div class="customer-container">
                    <div class="customer-header">
                        <h3><i class="fas fa-list"></i> Customer Directory</h3>
                        <div class="view-toggle">
                            <button class="view-btn active" onclick="setCustomerView('grid')" data-view="grid">
                                <i class="fas fa-th"></i>
                            </button>
                            <button class="view-btn" onclick="setCustomerView('list')" data-view="list">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>

                    <div class="customer-list grid-view" id="customerList">
                        <!-- Dynamic content -->
                    </div>
                </div>
            </section>

            <!-- Renewals Section -->
            <section id="renewals" class="content-section">
                <div class="section-header">
                    <div class="section-title">
                        <h2><i class="fas fa-sync-alt"></i> Renewal Management</h2>
                        <p>Track and manage customer renewals across all types</p>
                    </div>
                    <div class="section-actions">
                        <button class="btn btn-outline" onclick="sendBulkNotifications()" id="bulkNotifyBtn">
                            <i class="fas fa-bullhorn"></i> Bulk Notifications
                        </button>
                        <button class="btn btn-primary" onclick="createNewRenewal()" id="createRenewalBtn">
                            <i class="fas fa-plus"></i> Create New Renewal
                        </button>
                    </div>
                </div>

                <!-- Renewal Timeline Cards -->
                <div class="timeline-section">
                    <h3 class="timeline-section-title">
                        <i class="fas fa-chart-line"></i>
                        Renewal Revenue Timeline
                    </h3>

                    <div class="timeline-grid">
                        <!-- Today Card -->
                        <div class="timeline-card today-card">
                            <div class="timeline-header">
                                <div class="timeline-icon today">
                                    <i class="fas fa-calendar-day"></i>
                                </div>
                                <div class="timeline-info">
                                    <h4 class="timeline-title">Today</h4>
                                    <div class="timeline-subtitle">Renewal Revenue</div>
                                </div>
                            </div>
                            <div class="timeline-stats">
                                <div class="timeline-number" id="todayRenewals">$0</div>
                                <div class="timeline-target">Target: <span id="todayRenewalTarget">$25K</span></div>
                            </div>
                            <div class="timeline-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill today-progress" id="todayRenewalProgressBar" style="width: 0%"></div>
                                </div>
                                <div class="progress-label">
                                    <span id="todayRenewalPercentage">0%</span> of target achieved
                                </div>
                            </div>
                        </div>

                        <!-- Weekly Card -->
                        <div class="timeline-card weekly-card">
                            <div class="timeline-header">
                                <div class="timeline-icon weekly">
                                    <i class="fas fa-calendar-week"></i>
                                </div>
                                <div class="timeline-info">
                                    <h4 class="timeline-title">This Week</h4>
                                    <div class="timeline-subtitle">Renewal Revenue</div>
                                </div>
                            </div>
                            <div class="timeline-stats">
                                <div class="timeline-number" id="weeklyRenewals">$0</div>
                                <div class="timeline-target">Target: <span id="weeklyRenewalTarget">$125K</span></div>
                            </div>
                            <div class="timeline-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill weekly-progress" id="weeklyRenewalProgressBar" style="width: 0%"></div>
                                </div>
                                <div class="progress-label">
                                    <span id="weeklyRenewalPercentage">0%</span> of target achieved
                                </div>
                            </div>
                        </div>

                        <!-- MTD Card -->
                        <div class="timeline-card mtd-card">
                            <div class="timeline-header">
                                <div class="timeline-icon mtd">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div class="timeline-info">
                                    <h4 class="timeline-title">MTD</h4>
                                    <div class="timeline-subtitle">Month to Date Revenue</div>
                                </div>
                            </div>
                            <div class="timeline-stats">
                                <div class="timeline-number" id="mtdRenewals">$0</div>
                                <div class="timeline-target">Target: <span id="mtdRenewalTarget">$500K</span></div>
                            </div>
                            <div class="timeline-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill mtd-progress" id="mtdRenewalProgressBar" style="width: 0%"></div>
                                </div>
                                <div class="progress-label">
                                    <span id="mtdRenewalPercentage">0%</span> of target achieved
                                </div>
                            </div>
                        </div>

                        <!-- YTD Card -->
                        <div class="timeline-card ytd-card">
                            <div class="timeline-header">
                                <div class="timeline-icon ytd">
                                    <i class="fas fa-calendar"></i>
                                </div>
                                <div class="timeline-info">
                                    <h4 class="timeline-title">YTD</h4>
                                    <div class="timeline-subtitle">Year to Date Revenue</div>
                                </div>
                            </div>
                            <div class="timeline-stats">
                                <div class="timeline-number" id="ytdRenewals">$0</div>
                                <div class="timeline-target">Target: <span id="ytdRenewalTarget">$6M</span></div>
                            </div>
                            <div class="timeline-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill ytd-progress" id="ytdRenewalProgressBar" style="width: 0%"></div>
                                </div>
                                <div class="progress-label">
                                    <span id="ytdRenewalPercentage">0%</span> of target achieved
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Renewal Type Overview -->
                <div class="renewal-type-overview">
                    <div class="renewal-type-card" data-type="license" onclick="filterRenewalsByType('license')">
                        <div class="card-icon license">
                            <i class="fas fa-key"></i>
                        </div>
                        <div class="card-content">
                            <h3>License Renewals</h3>
                            <p>Software license renewals</p>
                            <div class="renewal-stats">
                                <div class="stat-item">
                                    <span class="stat-label">Received:</span>
                                    <span class="stat-value received" id="licenseReceived">$0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Pending:</span>
                                    <span class="stat-value pending" id="licensePending">$0</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>

                    <div class="renewal-type-card" data-type="support_activity" onclick="filterRenewalsByType('support_activity')">
                        <div class="card-icon support">
                            <i class="fas fa-headset"></i>
                        </div>
                        <div class="card-content">
                            <h3>Support Activity</h3>
                            <p>Support service renewals</p>
                            <div class="renewal-stats">
                                <div class="stat-item">
                                    <span class="stat-label">Received:</span>
                                    <span class="stat-value received" id="supportReceived">$0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Pending:</span>
                                    <span class="stat-value pending" id="supportPending">$0</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>

                    <div class="renewal-type-card" data-type="cr" onclick="filterRenewalsByType('cr')">
                        <div class="card-icon cr">
                            <i class="fas fa-edit"></i>
                        </div>
                        <div class="card-content">
                            <h3>Change Requests</h3>
                            <p>Change request renewals</p>
                            <div class="renewal-stats">
                                <div class="stat-item">
                                    <span class="stat-label">Received:</span>
                                    <span class="stat-value received" id="crReceived">$0</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Pending:</span>
                                    <span class="stat-value pending" id="crPending">$0</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>

                <!-- Renewal Filters -->
                <div class="filters-container">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="renewalSearch" placeholder="Search renewals..." onkeyup="searchRenewals()">
                    </div>

                    <div class="filter-group">
                        <div class="filter-item">
                            <label><i class="fas fa-filter"></i> Type</label>
                            <select id="renewalTypeFilter" onchange="filterRenewals()">
                                <option value="">All Types</option>
                                <option value="license">🔑 License</option>
                                <option value="support_activity">🎧 Support Activity</option>
                                <option value="cr">📝 Change Request</option>
                            </select>
                        </div>

                        <div class="filter-item">
                            <label><i class="fas fa-info-circle"></i> Status</label>
                            <select id="renewalStatusFilter" onchange="filterRenewals()">
                                <option value="">All Status</option>
                                <option value="pending">⏳ Pending</option>
                                <option value="received">✅ Received</option>
                                <option value="overdue">❌ Overdue</option>
                            </select>
                        </div>

                        <div class="filter-item">
                            <label><i class="fas fa-users"></i> Customer</label>
                            <select id="renewalCustomerFilter" onchange="filterRenewals()">
                                <option value="">All Customers</option>
                                <!-- Dynamic content -->
                            </select>
                        </div>

                        <button class="btn btn-outline" onclick="clearRenewalFilters()">
                            <i class="fas fa-times"></i> Clear
                        </button>
                    </div>
                </div>

                <!-- Renewals Table -->
                <div class="table-container">
                    <div class="table-header">
                        <h3><i class="fas fa-list"></i> Renewals List</h3>
                        <div class="table-actions">
                            <button class="btn btn-outline" onclick="exportRenewals()">
                                <i class="fas fa-download"></i> Export
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-hashtag"></i> Renewal ID</th>
                                    <th><i class="fas fa-building"></i> Customer</th>
                                    <th><i class="fas fa-tag"></i> Type</th>
                                    <th><i class="fas fa-dollar-sign"></i> Amount</th>
                                    <th><i class="fas fa-calendar"></i> Renewal Date</th>
                                    <th><i class="fas fa-info-circle"></i> Status</th>
                                    <th><i class="fas fa-cogs"></i> Actions</th>
                                </tr>
                            </thead>
                            <tbody id="renewalTableBody">
                                <!-- Dynamic content -->
                            </tbody>
                        </table>
                    </div>

                    <div class="table-footer">
                        <div class="table-info">
                            <span id="renewalTableInfo">Showing 0 of 0 renewals</span>
                        </div>
                        <div class="table-pagination">
                            <button class="btn btn-outline" onclick="previousRenewalPage()" id="prevRenewalBtn" disabled>
                                <i class="fas fa-chevron-left"></i> Previous
                            </button>
                            <span class="page-info" id="renewalPageInfo">Page 1 of 1</span>
                            <button class="btn btn-outline" onclick="nextRenewalPage()" id="nextRenewalBtn" disabled>
                                Next <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Onboarding Section -->
            <section id="onboarding" class="content-section">
                <div class="section-header">
                    <div class="section-title">
                        <h2><i class="fas fa-rocket"></i> Customer Onboarding</h2>
                        <p>Streamline your customer onboarding process</p>
                    </div>
                    <button class="btn btn-primary" onclick="createNewOnboarding()" id="createOnboardingBtn">
                        <i class="fas fa-plus"></i> Start New Onboarding
                    </button>
                </div>

                <!-- Onboarding Stats -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon pending">
                            <i class="fas fa-hourglass-half"></i>
                        </div>
                        <div class="stat-content">
                            <h3>In Progress</h3>
                            <span class="stat-number" id="onboardingInProgress">8</span>
                            <span class="stat-change neutral">Active onboardings</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon customers">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3>Completed</h3>
                            <span class="stat-number" id="onboardingCompleted">23</span>
                            <span class="stat-change positive">+3 this month</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon orders">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3>Avg. Time</h3>
                            <span class="stat-number" id="avgOnboardingTime">12</span>
                            <span class="stat-change positive">days to complete</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon revenue">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="stat-content">
                            <h3>Success Rate</h3>
                            <span class="stat-number" id="onboardingSuccessRate">94%</span>
                            <span class="stat-change positive">+2% this quarter</span>
                        </div>
                    </div>
                </div>

                <!-- Onboarding Process Steps -->
                <div class="onboarding-process">
                    <h3 class="section-subtitle">
                        <i class="fas fa-list-ol"></i>
                        Onboarding Process Steps
                    </h3>

                    <div class="process-steps">
                        <div class="process-step completed">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h4>Initial Contact</h4>
                                <p>Welcome email and account setup</p>
                                <div class="step-status">
                                    <i class="fas fa-check"></i> Automated
                                </div>
                            </div>
                        </div>

                        <div class="process-step active">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h4>Documentation Review</h4>
                                <p>Contract and requirement analysis</p>
                                <div class="step-status">
                                    <i class="fas fa-clock"></i> In Progress
                                </div>
                            </div>
                        </div>

                        <div class="process-step">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h4>System Configuration</h4>
                                <p>Platform setup and customization</p>
                                <div class="step-status">
                                    <i class="fas fa-hourglass-half"></i> Pending
                                </div>
                            </div>
                        </div>

                        <div class="process-step">
                            <div class="step-number">4</div>
                            <div class="step-content">
                                <h4>Training & Support</h4>
                                <p>User training and knowledge transfer</p>
                                <div class="step-status">
                                    <i class="fas fa-hourglass-half"></i> Pending
                                </div>
                            </div>
                        </div>

                        <div class="process-step">
                            <div class="step-number">5</div>
                            <div class="step-content">
                                <h4>Go Live</h4>
                                <p>Production deployment and monitoring</p>
                                <div class="step-status">
                                    <i class="fas fa-hourglass-half"></i> Pending
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Active Onboarding Projects -->
                <div class="table-container">
                    <div class="table-header">
                        <h3><i class="fas fa-list"></i> Active Onboarding Projects</h3>
                        <div class="table-actions">
                            <button class="btn btn-outline" onclick="exportOnboardings()">
                                <i class="fas fa-download"></i> Export
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-hashtag"></i> Project ID</th>
                                    <th><i class="fas fa-building"></i> Customer</th>
                                    <th><i class="fas fa-calendar"></i> Start Date</th>
                                    <th><i class="fas fa-calendar-check"></i> Target Date</th>
                                    <th><i class="fas fa-tasks"></i> Progress</th>
                                    <th><i class="fas fa-user"></i> Assigned To</th>
                                    <th><i class="fas fa-cogs"></i> Actions</th>
                                </tr>
                            </thead>
                            <tbody id="onboardingTableBody">
                                <!-- Dynamic content -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Professional Services Section -->
            <section id="professional-services" class="content-section">
                <div class="section-header">
                    <div class="section-title">
                        <h2><i class="fas fa-briefcase"></i> Professional Services</h2>
                        <p>Comprehensive professional services for your business needs</p>
                    </div>
                    <button class="btn btn-primary" onclick="requestService()" id="requestServiceBtn">
                        <i class="fas fa-plus"></i> Request Service
                    </button>
                </div>

                <!-- Services Overview -->
                <div class="services-grid">
                    <div class="service-card" data-service="deployment" onclick="showServiceDetails('deployment')">
                        <div class="service-icon deployment">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <div class="service-content">
                            <h3>Deployment</h3>
                            <p>Professional deployment services for seamless implementation</p>
                            <div class="service-stats">
                                <span class="stat-item">
                                    <i class="fas fa-clock"></i> Avg: 5-7 days
                                </span>
                                <span class="stat-item">
                                    <i class="fas fa-check"></i> 99% Success Rate
                                </span>
                            </div>
                        </div>
                        <div class="service-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>

                    <div class="service-card" data-service="infrastructure" onclick="showServiceDetails('infrastructure')">
                        <div class="service-icon infrastructure">
                            <i class="fas fa-server"></i>
                        </div>
                        <div class="service-content">
                            <h3>Infrastructure</h3>
                            <p>Infrastructure setup and optimization services</p>
                            <div class="service-stats">
                                <span class="stat-item">
                                    <i class="fas fa-clock"></i> Avg: 3-5 days
                                </span>
                                <span class="stat-item">
                                    <i class="fas fa-shield-alt"></i> 24/7 Support
                                </span>
                            </div>
                        </div>
                        <div class="service-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>

                    <div class="service-card" data-service="crs" onclick="showServiceDetails('crs')">
                        <div class="service-icon crs">
                            <i class="fas fa-edit"></i>
                        </div>
                        <div class="service-content">
                            <h3>Change Requests (CR's)</h3>
                            <p>Professional change request management and implementation</p>
                            <div class="service-stats">
                                <span class="stat-item">
                                    <i class="fas fa-clock"></i> Avg: 2-4 days
                                </span>
                                <span class="stat-item">
                                    <i class="fas fa-users"></i> Expert Team
                                </span>
                            </div>
                        </div>
                        <div class="service-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>

                    <div class="service-card" data-service="support" onclick="showServiceDetails('support')">
                        <div class="service-icon support">
                            <i class="fas fa-headset"></i>
                        </div>
                        <div class="service-content">
                            <h3>Support</h3>
                            <p>Comprehensive technical support and maintenance services</p>
                            <div class="service-stats">
                                <span class="stat-item">
                                    <i class="fas fa-clock"></i> 24/7 Available
                                </span>
                                <span class="stat-item">
                                    <i class="fas fa-phone"></i> Multi-channel
                                </span>
                            </div>
                        </div>
                        <div class="service-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>

                    <div class="service-card" data-service="migration" onclick="showServiceDetails('migration')">
                        <div class="service-icon migration">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <div class="service-content">
                            <h3>Migration</h3>
                            <p>Seamless data and system migration services</p>
                            <div class="service-stats">
                                <span class="stat-item">
                                    <i class="fas fa-clock"></i> Avg: 7-10 days
                                </span>
                                <span class="stat-item">
                                    <i class="fas fa-lock"></i> Secure Process
                                </span>
                            </div>
                        </div>
                        <div class="service-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>

                    <div class="service-card" data-service="setup" onclick="showServiceDetails('setup')">
                        <div class="service-icon setup">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="service-content">
                            <h3>Set up Activity</h3>
                            <p>Complete system setup and configuration services</p>
                            <div class="service-stats">
                                <span class="stat-item">
                                    <i class="fas fa-clock"></i> Avg: 4-6 days
                                </span>
                                <span class="stat-item">
                                    <i class="fas fa-tools"></i> Custom Config
                                </span>
                            </div>
                        </div>
                        <div class="service-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>

                <!-- Service Requests Table -->
                <div class="table-container">
                    <div class="table-header">
                        <h3><i class="fas fa-list"></i> Service Requests</h3>
                        <div class="table-actions">
                            <div class="filter-group">
                                <select id="serviceStatusFilter" onchange="filterServiceRequests()">
                                    <option value="">All Status</option>
                                    <option value="pending">⏳ Pending</option>
                                    <option value="in-progress">🔄 In Progress</option>
                                    <option value="completed">✅ Completed</option>
                                    <option value="cancelled">❌ Cancelled</option>
                                </select>
                            </div>
                            <button class="btn btn-outline" onclick="exportServiceRequests()">
                                <i class="fas fa-download"></i> Export
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-hashtag"></i> Request ID</th>
                                    <th><i class="fas fa-briefcase"></i> Service Type</th>
                                    <th><i class="fas fa-building"></i> Customer</th>
                                    <th><i class="fas fa-calendar"></i> Request Date</th>
                                    <th><i class="fas fa-calendar-check"></i> Due Date</th>
                                    <th><i class="fas fa-info-circle"></i> Status</th>
                                    <th><i class="fas fa-user"></i> Assigned To</th>
                                    <th><i class="fas fa-cogs"></i> Actions</th>
                                </tr>
                            </thead>
                            <tbody id="serviceRequestsTableBody">
                                <!-- Dynamic content -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Admin Section -->
            <section id="admin" class="content-section admin-only">
                <div class="section-header">
                    <div class="section-title">
                        <h2><i class="fas fa-cog"></i> Admin Panel</h2>
                        <p>System administration and configuration</p>
                    </div>
                </div>

                <div class="admin-grid">
                    <div class="admin-card">
                        <div class="card-header">
                            <h3><i class="fas fa-users-cog"></i> User Management</h3>
                        </div>
                        <div class="card-content">
                            <div class="user-roles">
                                <div class="role-item">
                                    <div class="role-info">
                                        <i class="fas fa-crown"></i>
                                        <span class="role-name">Admin</span>
                                    </div>
                                    <span class="role-count" id="adminCount">2</span>
                                </div>
                                <div class="role-item">
                                    <div class="role-info">
                                        <i class="fas fa-eye"></i>
                                        <span class="role-name">Read Only</span>
                                    </div>
                                    <span class="role-count" id="readCount">8</span>
                                </div>
                                <div class="role-item">
                                    <div class="role-info">
                                        <i class="fas fa-edit"></i>
                                        <span class="role-name">Read & Write</span>
                                    </div>
                                    <span class="role-count" id="readWriteCount">15</span>
                                </div>
                            </div>
                            <button class="btn btn-primary" onclick="manageUsers()">
                                <i class="fas fa-users-cog"></i> Manage Users
                            </button>
                        </div>
                    </div>

                    <div class="admin-card">
                        <div class="card-header">
                            <h3><i class="fas fa-sliders-h"></i> Platform Settings</h3>
                        </div>
                        <div class="card-content">
                            <div class="platform-settings">
                                <div class="setting-item">
                                    <label><i class="fas fa-layer-group"></i> Default Platform:</label>
                                    <select id="defaultPlatform">
                                        <option value="silver">🥈 Silver</option>
                                        <option value="gold">🥇 Gold</option>
                                        <option value="platinum">💎 Platinum</option>
                                    </select>
                                </div>
                                <div class="setting-item">
                                    <label><i class="fas fa-check-circle"></i> Approval Workflow:</label>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="approvalRequired" checked>
                                        <label for="approvalRequired" class="toggle-label">
                                            <span class="toggle-slider"></span>
                                        </label>
                                        <span>Require approval for Read & Write users</span>
                                    </div>
                                </div>
                            </div>
                            <button class="btn btn-success" onclick="saveSettings()">
                                <i class="fas fa-save"></i> Save Settings
                            </button>
                        </div>
                    </div>

                    <div class="admin-card">
                        <div class="card-header">
                            <h3><i class="fas fa-chart-bar"></i> System Analytics</h3>
                        </div>
                        <div class="card-content">
                            <div class="analytics-grid">
                                <div class="analytics-item">
                                    <i class="fas fa-database"></i>
                                    <div class="analytics-info">
                                        <span class="analytics-label">Total Records</span>
                                        <span class="analytics-value" id="totalRecords">1,234</span>
                                    </div>
                                </div>
                                <div class="analytics-item">
                                    <i class="fas fa-clock"></i>
                                    <div class="analytics-info">
                                        <span class="analytics-label">System Uptime</span>
                                        <span class="analytics-value">99.9%</span>
                                    </div>
                                </div>
                                <div class="analytics-item">
                                    <i class="fas fa-shield-alt"></i>
                                    <div class="analytics-info">
                                        <span class="analytics-label">Security Score</span>
                                        <span class="analytics-value">A+</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="admin-card">
                        <div class="card-header">
                            <h3><i class="fas fa-tools"></i> Quick Actions</h3>
                        </div>
                        <div class="card-content">
                            <div class="admin-actions">
                                <button class="admin-action-btn" onclick="exportData()">
                                    <i class="fas fa-download"></i>
                                    <span>Export Data</span>
                                </button>
                                <button class="admin-action-btn" onclick="systemBackup()">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <span>System Backup</span>
                                </button>
                                <button class="admin-action-btn" onclick="viewLogs()">
                                    <i class="fas fa-file-alt"></i>
                                    <span>View Logs</span>
                                </button>
                                <button class="admin-action-btn" onclick="systemHealth()">
                                    <i class="fas fa-heartbeat"></i>
                                    <span>System Health</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Mobile Overlay -->
        <div class="mobile-overlay" id="mobileOverlay" onclick="toggleMobileMenu()"></div>
    </div>

    <!-- Enhanced Modal -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Modal Title</h3>
                <button class="modal-close" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Dynamic modal content -->
            </div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div id="loadingSpinner" class="loading-spinner">
        <div class="spinner">
            <i class="fas fa-spinner fa-spin"></i>
        </div>
        <p>Loading...</p>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container"></div>

    <script src="js/data.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/app.js"></script>
    <script src="js/purchase-orders.js"></script>
    <script src="js/customer-management.js"></script>
    <script src="js/user-management.js"></script>
    <script src="js/renewal-management.js"></script>
    <script src="js/po-charts.js"></script>
</body>
</html>