// Purchase Order management functionality

// Create new Purchase Order
function createNewPO() {
    if (!auth.canWrite()) {
        showNotification('You do not have permission to create purchase orders.', 'error');
        return;
    }
    
    const customers = getCustomers();
    const modalBody = document.getElementById('modalBody');
    
    modalBody.innerHTML = `
        <h2>Create New Purchase Order</h2>
        <form id="poForm" onsubmit="savePO(event)">
            <div class="form-row">
                <div class="form-group">
                    <label for="poCustomer">Customer:</label>
                    <select id="poCustomer" required onchange="updateCustomerInfo()">
                        <option value="">Select Customer</option>
                        ${customers.map(c => `<option value="${c.id}" data-type="${c.type}">${c.name} (${c.type})</option>`).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label for="poNotes">Notes:</label>
                    <input type="text" id="poNotes" placeholder="Optional notes">
                </div>
            </div>
            
            <div id="customerInfo" style="display: none; margin: 1rem 0; padding: 1rem; background: #f8f9fa; border-radius: 5px;">
                <h4>Customer Information</h4>
                <div id="customerDetails"></div>
            </div>
            
            <div class="form-group">
                <label>Purchase Order Items:</label>
                <div class="po-items" id="poItems">
                    <div class="po-item">
                        <div class="form-group">
                            <label>Description:</label>
                            <input type="text" class="item-description" required placeholder="Item description">
                        </div>
                        <div class="form-group">
                            <label>Quantity:</label>
                            <input type="number" class="item-quantity" required min="1" value="1" onchange="calculateItemTotal(this)">
                        </div>
                        <div class="form-group">
                            <label>Unit Price:</label>
                            <input type="number" class="item-price" required min="0" step="0.01" onchange="calculateItemTotal(this)">
                        </div>
                        <div class="form-group">
                            <label>Total:</label>
                            <input type="number" class="item-total" readonly>
                        </div>
                        <button type="button" class="remove-item" onclick="removeItem(this)" style="display: none;">Remove</button>
                    </div>
                </div>
                <button type="button" class="add-item" onclick="addItem()">Add Item</button>
            </div>
            
            <div class="po-total">
                <strong>Total Amount: $<span id="poTotalAmount">0.00</span></strong>
            </div>
            
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                <button type="submit" class="btn btn-primary">Create Purchase Order</button>
            </div>
        </form>
    `;
    
    showModal();
}

// Update customer information display
function updateCustomerInfo() {
    const customerSelect = document.getElementById('poCustomer');
    const customerInfo = document.getElementById('customerInfo');
    const customerDetails = document.getElementById('customerDetails');
    
    if (customerSelect.value) {
        const customer = getCustomerById(parseInt(customerSelect.value));
        if (customer) {
            customerDetails.innerHTML = `
                <p><strong>Type:</strong> ${CUSTOMER_TYPES[customer.type].name}</p>
                <p><strong>Email:</strong> ${customer.email}</p>
                <p><strong>Phone:</strong> ${customer.phone}</p>
                ${customer.renewalTypes.length > 0 ? 
                    `<p><strong>Renewal Types:</strong> ${customer.renewalTypes.map(type => RENEWAL_TYPES[type]).join(', ')}</p>` 
                    : ''}
            `;
            customerInfo.style.display = 'block';
        }
    } else {
        customerInfo.style.display = 'none';
    }
}

// Add new item to PO
function addItem() {
    const itemsContainer = document.getElementById('poItems');
    const newItem = document.createElement('div');
    newItem.className = 'po-item';
    newItem.innerHTML = `
        <div class="form-group">
            <label>Description:</label>
            <input type="text" class="item-description" required placeholder="Item description">
        </div>
        <div class="form-group">
            <label>Quantity:</label>
            <input type="number" class="item-quantity" required min="1" value="1" onchange="calculateItemTotal(this)">
        </div>
        <div class="form-group">
            <label>Unit Price:</label>
            <input type="number" class="item-price" required min="0" step="0.01" onchange="calculateItemTotal(this)">
        </div>
        <div class="form-group">
            <label>Total:</label>
            <input type="number" class="item-total" readonly>
        </div>
        <button type="button" class="remove-item" onclick="removeItem(this)">Remove</button>
    `;
    
    itemsContainer.appendChild(newItem);
    updateRemoveButtons();
}

// Remove item from PO
function removeItem(button) {
    const item = button.closest('.po-item');
    item.remove();
    updateRemoveButtons();
    calculateTotal();
}

// Update visibility of remove buttons
function updateRemoveButtons() {
    const items = document.querySelectorAll('.po-item');
    items.forEach((item, index) => {
        const removeBtn = item.querySelector('.remove-item');
        removeBtn.style.display = items.length > 1 ? 'block' : 'none';
    });
}

// Calculate item total
function calculateItemTotal(input) {
    const item = input.closest('.po-item');
    const quantity = parseFloat(item.querySelector('.item-quantity').value) || 0;
    const price = parseFloat(item.querySelector('.item-price').value) || 0;
    const total = quantity * price;
    
    item.querySelector('.item-total').value = total.toFixed(2);
    calculateTotal();
}

// Calculate PO total
function calculateTotal() {
    const totals = document.querySelectorAll('.item-total');
    let grandTotal = 0;
    
    totals.forEach(total => {
        grandTotal += parseFloat(total.value) || 0;
    });
    
    document.getElementById('poTotalAmount').textContent = grandTotal.toFixed(2);
}

// Save Purchase Order
function savePO(event) {
    event.preventDefault();
    
    const customerId = parseInt(document.getElementById('poCustomer').value);
    const notes = document.getElementById('poNotes').value;
    const customer = getCustomerById(customerId);
    
    if (!customer) {
        showNotification('Please select a valid customer.', 'error');
        return;
    }
    
    // Collect items
    const items = [];
    const itemElements = document.querySelectorAll('.po-item');
    
    itemElements.forEach(item => {
        const description = item.querySelector('.item-description').value;
        const quantity = parseInt(item.querySelector('.item-quantity').value);
        const unitPrice = parseFloat(item.querySelector('.item-price').value);
        const total = parseFloat(item.querySelector('.item-total').value);
        
        if (description && quantity && unitPrice) {
            items.push({
                description,
                quantity,
                unitPrice,
                total
            });
        }
    });
    
    if (items.length === 0) {
        showNotification('Please add at least one item.', 'error');
        return;
    }
    
    const totalAmount = items.reduce((sum, item) => sum + item.total, 0);
    
    const newPO = {
        customerId: customerId,
        customerName: customer.name,
        customerType: customer.type,
        amount: totalAmount,
        items: items,
        notes: notes
    };
    
    const savedPO = addPurchaseOrder(newPO);
    saveToLocalStorage();
    
    closeModal();
    showNotification('Purchase Order created successfully!', 'success');
    
    // Refresh the current view
    if (currentSection === 'purchase-orders') {
        loadPurchaseOrders();
    } else if (currentSection === 'dashboard') {
        loadDashboard();
    }
}

// View Purchase Order
function viewPO(poId) {
    const po = getPOById(poId);
    if (!po) {
        showNotification('Purchase Order not found.', 'error');
        return;
    }
    
    const customer = getCustomerById(po.customerId);
    const modalBody = document.getElementById('modalBody');
    
    modalBody.innerHTML = `
        <h2>Purchase Order Details</h2>
        <div class="po-details">
            <div class="form-row">
                <div class="form-group">
                    <label>PO Number:</label>
                    <div>${po.id}</div>
                </div>
                <div class="form-group">
                    <label>Status:</label>
                    <span class="status-badge status-${po.status}">${PO_STATUSES[po.status]}</span>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label>Customer:</label>
                    <div>${po.customerName} (${po.customerType})</div>
                </div>
                <div class="form-group">
                    <label>Created:</label>
                    <div>${formatDate(po.created)}</div>
                </div>
            </div>
            
            ${po.notes ? `
                <div class="form-group">
                    <label>Notes:</label>
                    <div>${po.notes}</div>
                </div>
            ` : ''}
            
            <div class="form-group">
                <label>Items:</label>
                <table class="po-table">
                    <thead>
                        <tr>
                            <th>Description</th>
                            <th>Quantity</th>
                            <th>Unit Price</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${po.items.map(item => `
                            <tr>
                                <td>${item.description}</td>
                                <td>${item.quantity}</td>
                                <td>$${item.unitPrice.toFixed(2)}</td>
                                <td>$${item.total.toFixed(2)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
            
            <div class="po-total">
                <strong>Total Amount: $${po.amount.toFixed(2)}</strong>
            </div>
            
            ${po.approvals.length > 0 ? `
                <div class="form-group">
                    <label>Approval History:</label>
                    <div class="approval-history">
                        ${po.approvals.map(approval => `
                            <div class="approval-item">
                                <strong>${approval.userName}</strong> ${approval.action} on ${formatDate(approval.date)}
                                ${approval.notes ? `<br><em>${approval.notes}</em>` : ''}
                            </div>
                        `).join('')}
                    </div>
                </div>
            ` : ''}
            
            ${po.status === 'pending' && auth.canApprove() ? `
                <div class="workflow-status">
                    <div class="workflow-step active">Pending Approval</div>
                    <div class="workflow-arrow">→</div>
                    <div class="workflow-step">Approved/Rejected</div>
                </div>
            ` : ''}
        </div>
        
        <div class="form-actions">
            <button type="button" class="btn btn-secondary" onclick="closeModal()">Close</button>
            ${auth.canWrite() && po.status === 'draft' ? `<button type="button" class="btn btn-warning" onclick="editPO('${po.id}')">Edit</button>` : ''}
            ${auth.canApprove() && po.status === 'pending' ? `
                <button type="button" class="btn btn-success" onclick="approvePO('${po.id}')">Approve</button>
                <button type="button" class="btn btn-danger" onclick="rejectPO('${po.id}')">Reject</button>
            ` : ''}
        </div>
    `;
    
    showModal();
}

// Edit Purchase Order
function editPO(poId) {
    if (!auth.canWrite()) {
        showNotification('You do not have permission to edit purchase orders.', 'error');
        return;
    }
    
    const po = getPOById(poId);
    if (!po || po.status !== 'draft') {
        showNotification('Only draft purchase orders can be edited.', 'error');
        return;
    }
    
    // Implementation for editing would be similar to createNewPO but pre-filled
    showNotification('Edit functionality would be implemented here.', 'info');
}

// Approve Purchase Order
function approvePO(poId) {
    if (!auth.canApprove()) {
        showNotification('You do not have permission to approve purchase orders.', 'error');
        return;
    }
    
    const modalBody = document.getElementById('modalBody');
    modalBody.innerHTML = `
        <h2>Approve Purchase Order</h2>
        <p>Are you sure you want to approve PO ${poId}?</p>
        <form onsubmit="confirmApproval(event, '${poId}', 'approved')">
            <div class="form-group">
                <label for="approvalNotes">Approval Notes (optional):</label>
                <textarea id="approvalNotes" placeholder="Add any notes about this approval..."></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                <button type="submit" class="btn btn-success">Approve</button>
            </div>
        </form>
    `;
    
    showModal();
}

// Reject Purchase Order
function rejectPO(poId) {
    if (!auth.canApprove()) {
        showNotification('You do not have permission to reject purchase orders.', 'error');
        return;
    }
    
    const modalBody = document.getElementById('modalBody');
    modalBody.innerHTML = `
        <h2>Reject Purchase Order</h2>
        <p>Are you sure you want to reject PO ${poId}?</p>
        <form onsubmit="confirmApproval(event, '${poId}', 'rejected')">
            <div class="form-group">
                <label for="approvalNotes">Rejection Reason:</label>
                <textarea id="approvalNotes" required placeholder="Please provide a reason for rejection..."></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                <button type="submit" class="btn btn-danger">Reject</button>
            </div>
        </form>
    `;
    
    showModal();
}

// Confirm approval/rejection
function confirmApproval(event, poId, action) {
    event.preventDefault();
    
    const notes = document.getElementById('approvalNotes').value;
    const currentUser = getCurrentUser();
    
    const approval = {
        userId: currentUser.id,
        userName: currentUser.name,
        action: action,
        notes: notes
    };
    
    const updatedPO = approvePurchaseOrder(poId, approval);
    
    if (updatedPO) {
        saveToLocalStorage();
        closeModal();
        showNotification(`Purchase Order ${action} successfully!`, 'success');
        
        // Refresh the current view
        if (currentSection === 'purchase-orders') {
            loadPurchaseOrders();
        } else if (currentSection === 'dashboard') {
            loadDashboard();
        }
    } else {
        showNotification('Error processing approval.', 'error');
    }
}
