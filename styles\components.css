/* Enhanced Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--white);
    border-radius: var(--radius-xl);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    animation: slideIn 0.3s ease;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--gray-50);
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
}

.modal-close {
    background: var(--gray-100);
    border: none;
    padding: var(--space-2);
    border-radius: var(--radius);
    cursor: pointer;
    color: var(--gray-500);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
}

.modal-close:hover {
    background: var(--error);
    color: var(--white);
}

.modal-body {
    padding: var(--space-6);
    overflow-y: auto;
    flex: 1;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        transform: translateY(-20px) scale(0.95);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

/* Enhanced Form Styles */
.form-group {
    margin-bottom: var(--space-5);
}

.form-group label {
    display: block;
    margin-bottom: var(--space-2);
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--space-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius);
    font-size: 0.9rem;
    transition: all 0.2s ease;
    background: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
    background: var(--white);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
    font-family: inherit;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-4);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--space-3);
    margin-top: var(--space-6);
    padding-top: var(--space-4);
    border-top: 1px solid var(--gray-200);
}

/* Search Box */
.search-box {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.search-box i {
    position: absolute;
    left: var(--space-3);
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
}

.search-box input {
    width: 100%;
    padding: var(--space-3) var(--space-3) var(--space-3) var(--space-10);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

/* Filters */
.filters-container {
    background: var(--white);
    padding: var(--space-6);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    margin-bottom: var(--space-6);
    border: 1px solid var(--gray-200);
}

.filter-group {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    flex-wrap: wrap;
}

.filter-item {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.filter-item label {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.filter-item select {
    min-width: 150px;
    padding: var(--space-2) var(--space-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius);
    font-size: 0.9rem;
}

/* Filter Buttons */
.filter-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    border: 1px solid var(--gray-300);
    background: var(--white);
    color: var(--gray-600);
    border-radius: var(--radius);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
    font-weight: 500;
}

.filter-btn:hover {
    background: var(--gray-50);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.filter-btn.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

/* Customer Controls */
.customer-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--space-4);
    margin-bottom: var(--space-6);
    flex-wrap: wrap;
}

/* Enhanced Table Styles */
.table-container {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.table-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--gray-50);
}

.table-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.table-actions {
    display: flex;
    gap: var(--space-3);
}

.table-responsive {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.data-table th,
.data-table td {
    padding: var(--space-4);
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

.data-table th {
    background: var(--gray-50);
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.data-table tbody tr {
    transition: background-color 0.2s ease;
}

.data-table tbody tr:hover {
    background: var(--gray-50);
}

.table-footer {
    padding: var(--space-4) var(--space-6);
    border-top: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--gray-50);
    font-size: 0.9rem;
}

.table-pagination {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.page-info {
    color: var(--gray-600);
    font-weight: 500;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-lg);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-draft {
    background: var(--gray-100);
    color: var(--gray-600);
}

.status-pending {
    background: #fef3c7;
    color: #92400e;
}

.status-approved {
    background: #d1fae5;
    color: #065f46;
}

.status-rejected {
    background: #fee2e2;
    color: #991b1b;
}

/* Customer Type Badges */
.customer-type-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-lg);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.customer-type-nn {
    background: #dbeafe;
    color: #1e40af;
}

.customer-type-en {
    background: #f3e8ff;
    color: #7c3aed;
}

.customer-type-ee {
    background: #fed7aa;
    color: #ea580c;
}

/* Action Buttons */
.action-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
    padding: var(--space-1) var(--space-3);
    border: none;
    border-radius: var(--radius);
    cursor: pointer;
    font-size: 0.8rem;
    font-weight: 500;
    margin-right: var(--space-2);
    transition: all 0.2s ease;
}

.action-btn:last-child {
    margin-right: 0;
}

.action-view {
    background: var(--info);
    color: var(--white);
}

.action-view:hover {
    background: #2563eb;
}

.action-edit {
    background: var(--warning);
    color: var(--white);
}

.action-edit:hover {
    background: #d97706;
}

.action-delete {
    background: var(--error);
    color: var(--white);
}

.action-delete:hover {
    background: #dc2626;
}

.action-approve {
    background: var(--success);
    color: var(--white);
}

.action-approve:hover {
    background: #059669;
}

.action-reject {
    background: var(--error);
    color: var(--white);
}

.action-reject:hover {
    background: #dc2626;
}

/* Customer Overview */
.customer-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.customer-type-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--space-4);
    position: relative;
    overflow: hidden;
}

.customer-type-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.customer-type-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-color);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.customer-type-card:hover::before {
    transform: scaleY(1);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--white);
}

.card-icon.nn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.card-icon.en {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.card-icon.ee {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.card-content {
    flex: 1;
}

.card-content h3 {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.card-content p {
    color: var(--gray-600);
    font-size: 0.9rem;
    margin-bottom: var(--space-3);
}

.customer-count {
    font-size: 2rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: var(--space-2);
}

.renewal-types {
    display: flex;
    gap: var(--space-2);
    flex-wrap: wrap;
}

.renewal-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
    padding: var(--space-1) var(--space-2);
    background: var(--gray-100);
    color: var(--gray-600);
    border-radius: var(--radius);
    font-size: 0.7rem;
    font-weight: 500;
}

.card-arrow {
    color: var(--gray-400);
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.customer-type-card:hover .card-arrow {
    color: var(--primary-color);
    transform: translateX(4px);
}

/* Customer Container */
.customer-container {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.customer-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--gray-50);
}

.customer-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.view-toggle {
    display: flex;
    background: var(--gray-200);
    border-radius: var(--radius);
    padding: 2px;
}

.view-btn {
    background: transparent;
    border: none;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-sm);
    cursor: pointer;
    color: var(--gray-600);
    transition: all 0.2s ease;
}

.view-btn.active {
    background: var(--white);
    color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

/* Customer List Views */
.customer-list {
    padding: var(--space-6);
}

.customer-list.grid-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--space-4);
}

.customer-list.list-view {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.customer-item {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--space-5);
    transition: all 0.2s ease;
    cursor: pointer;
}

.customer-item:hover {
    background: var(--white);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.customer-list.list-view .customer-item {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr auto;
    gap: var(--space-4);
    align-items: center;
    padding: var(--space-4);
}

.customer-list.grid-view .customer-item {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
    text-align: center;
}

/* Renewal Options */
.renewal-options {
    display: none;
    margin-top: var(--space-4);
    padding: var(--space-4);
    background: var(--gray-50);
    border-radius: var(--radius);
    border: 1px solid var(--gray-200);
}

.renewal-options.show {
    display: block;
}

.renewal-option {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-3);
}

.renewal-option:last-child {
    margin-bottom: 0;
}

.renewal-option input {
    width: auto;
    margin: 0;
}

.renewal-option label {
    margin: 0;
    font-weight: 500;
    color: var(--gray-700);
}

/* User Management */
.user-list {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.user-item {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr auto;
    gap: 1rem;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #eee;
}

.user-item:last-child {
    border-bottom: none;
}

.user-actions {
    display: flex;
    gap: 0.5rem;
}

.user-actions button {
    padding: 0.25rem 0.75rem;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 0.8rem;
}

/* Platform Features */
.platform-features {
    list-style: none;
}

.platform-features li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
    position: relative;
    padding-left: 1.5rem;
}

.platform-features li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #28a745;
    font-weight: bold;
}

.platform-features li.disabled {
    color: #999;
    text-decoration: line-through;
}

.platform-features li.disabled:before {
    content: "✗";
    color: #dc3545;
}

/* Recent PO List */
.po-list {
    max-height: 300px;
    overflow-y: auto;
}

.po-item-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.3s;
}

.po-item-summary:hover {
    background: #f8f9fa;
}

.po-item-summary:last-child {
    border-bottom: none;
}

.po-number {
    font-weight: 600;
    color: #667eea;
}

.po-amount {
    font-weight: 500;
}

/* Customer List */
.customer-list {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.customer-item {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr auto;
    gap: 1rem;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #eee;
}

.customer-item:last-child {
    border-bottom: none;
}

.customer-type-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.customer-type-nn {
    background: #e3f2fd;
    color: #1976d2;
}

.customer-type-en {
    background: #f3e5f5;
    color: #7b1fa2;
}

.customer-type-ee {
    background: #fff3e0;
    color: #f57c00;
}

/* Workflow Status */
.workflow-status {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 1rem 0;
}

.workflow-step {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    background: #f8f9fa;
    color: #6c757d;
    font-size: 0.9rem;
}

.workflow-step.active {
    background: #667eea;
    color: white;
}

.workflow-step.completed {
    background: #28a745;
    color: white;
}

.workflow-arrow {
    color: #ddd;
    font-size: 1.2rem;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 5px;
    color: white;
    font-weight: 500;
    z-index: 1001;
    animation: slideInRight 0.3s;
}

.notification.success {
    background: #28a745;
}

.notification.error {
    background: #dc3545;
}

.notification.warning {
    background: #ffc107;
    color: #212529;
}

.notification.info {
    background: #17a2b8;
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

/* Loading Spinner */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Action Buttons */
.action-btn {
    padding: 0.25rem 0.75rem;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 0.8rem;
    margin-right: 0.5rem;
}

.action-btn:last-child {
    margin-right: 0;
}

.action-view {
    background: #17a2b8;
    color: white;
}

.action-edit {
    background: #ffc107;
    color: #212529;
}

.action-delete {
    background: #dc3545;
    color: white;
}

.action-approve {
    background: #28a745;
    color: white;
}

.action-reject {
    background: #dc3545;
    color: white;
}

/* Admin Panel Styles */
.admin-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-6);
}

.admin-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: all 0.3s ease;
}

.admin-card:hover {
    box-shadow: var(--shadow-lg);
}

.role-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4);
    border-bottom: 1px solid var(--gray-200);
}

.role-item:last-child {
    border-bottom: none;
}

.role-info {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.role-info i {
    color: var(--primary-color);
}

.role-name {
    font-weight: 500;
    color: var(--gray-700);
}

.role-count {
    background: var(--primary-color);
    color: var(--white);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-lg);
    font-size: 0.8rem;
    font-weight: 600;
    min-width: 24px;
    text-align: center;
}

/* Platform Settings */
.platform-settings {
    display: flex;
    flex-direction: column;
    gap: var(--space-5);
}

.setting-item {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.setting-item label {
    font-weight: 600;
    color: var(--gray-700);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.toggle-switch {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.toggle-switch input[type="checkbox"] {
    display: none;
}

.toggle-label {
    position: relative;
    width: 50px;
    height: 24px;
    background: var(--gray-300);
    border-radius: 12px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.toggle-slider {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: var(--white);
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.toggle-switch input[type="checkbox"]:checked + .toggle-label {
    background: var(--primary-color);
}

.toggle-switch input[type="checkbox"]:checked + .toggle-label .toggle-slider {
    transform: translateX(26px);
}

/* Analytics */
.analytics-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-4);
}

.analytics-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-4);
    background: var(--gray-50);
    border-radius: var(--radius);
}

.analytics-item i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.analytics-info {
    display: flex;
    flex-direction: column;
}

.analytics-label {
    font-size: 0.8rem;
    color: var(--gray-600);
    font-weight: 500;
}

.analytics-value {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--gray-900);
}

/* Admin Actions */
.admin-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-3);
}

.admin-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-4);
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius);
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    color: var(--gray-700);
}

.admin-action-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.admin-action-btn i {
    font-size: 1.2rem;
}

.admin-action-btn span {
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--space-6);
    right: var(--space-6);
    z-index: 1001;
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.toast {
    background: var(--white);
    border-radius: var(--radius);
    box-shadow: var(--shadow-lg);
    padding: var(--space-4);
    border-left: 4px solid var(--primary-color);
    min-width: 300px;
    animation: slideInRight 0.3s ease;
}

.toast.success {
    border-left-color: var(--success);
}

.toast.error {
    border-left-color: var(--error);
}

.toast.warning {
    border-left-color: var(--warning);
}

/* Loading Spinner */
.loading-spinner {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: none;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    z-index: 1002;
}

.loading-spinner.show {
    display: flex;
}

.spinner {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: var(--space-4);
}

/* Renewal Type Overview */
.renewal-type-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.renewal-type-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--space-4);
    position: relative;
    overflow: hidden;
}

.renewal-type-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.renewal-type-card .card-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--white);
    flex-shrink: 0;
}

.renewal-type-card .card-icon.license {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.renewal-type-card .card-icon.support {
    background: linear-gradient(135deg, #10b981, #047857);
}

.renewal-type-card .card-icon.cr {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.renewal-type-card .card-content {
    flex: 1;
}

.renewal-type-card .card-content h3 {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.renewal-type-card .card-content p {
    color: var(--gray-600);
    font-size: 0.9rem;
    margin-bottom: var(--space-3);
}

.renewal-stats {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-label {
    font-size: 0.8rem;
    color: var(--gray-600);
    font-weight: 500;
}

.stat-value {
    font-weight: 700;
    font-size: 0.9rem;
}

.stat-value.received {
    color: var(--success);
}

.stat-value.pending {
    color: var(--warning);
}

.renewal-type-card .card-arrow {
    color: var(--gray-400);
    font-size: 1.2rem;
    transition: all 0.2s ease;
}

/* Analytics Section */
.analytics-section {
    margin-bottom: var(--space-8);
}

.analytics-section-title {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-6);
    color: var(--gray-900);
    font-size: 1.25rem;
    font-weight: 600;
}

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-6);
}

.analytics-card {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: all 0.2s ease;
}

.analytics-card:hover {
    box-shadow: var(--shadow-md);
}

.analytics-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-3);
    border-bottom: 1px solid var(--gray-200);
}

.analytics-header h4 {
    margin: 0;
    color: var(--gray-900);
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

.chart-container canvas {
    max-width: 100%;
    max-height: 100%;
}

.chart-controls {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.chart-controls select {
    padding: var(--space-1) var(--space-2);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    background: white;
}

.chart-legend {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    font-size: 0.875rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

.legend-color.nn {
    background: #3b82f6;
}

.legend-color.en {
    background: #8b5cf6;
}

.legend-color.ee {
    background: #f59e0b;
}

.btn-sm {
    padding: var(--space-1) var(--space-2);
    font-size: 0.875rem;
}

/* Responsive adjustments for analytics */
@media (max-width: 768px) {
    .analytics-grid {
        grid-template-columns: 1fr;
    }

    .analytics-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-2);
    }

    .chart-container {
        height: 250px;
    }
}

.renewal-type-card:hover .card-arrow {
    color: var(--primary-color);
    transform: translateX(4px);
}

/* Renewal Details */
.renewal-details {
    max-height: 70vh;
    overflow-y: auto;
}

.detail-section {
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--gray-200);
}

.detail-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.detail-section h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--space-4);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
}

.detail-item label {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.detail-item span {
    font-size: 0.9rem;
    color: var(--gray-900);
}

.amount-display {
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--success);
}

.description-text {
    background: var(--gray-50);
    padding: var(--space-4);
    border-radius: var(--radius);
    border-left: 4px solid var(--primary-color);
    font-style: italic;
    color: var(--gray-700);
    margin: 0;
}

.overdue {
    color: var(--error) !important;
    font-weight: 600;
}

.overdue-indicator {
    margin-left: var(--space-2);
    color: var(--warning);
}

/* Modal Actions */
.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--space-3);
    margin-top: var(--space-6);
    padding-top: var(--space-4);
    border-top: 1px solid var(--gray-200);
}

/* Renewal Status Badges */
.status-received {
    background: #d1fae5;
    color: #065f46;
}

.status-overdue {
    background: #fee2e2;
    color: #991b1b;
}

.status-cancelled {
    background: var(--gray-100);
    color: var(--gray-600);
}

/* Notification Styles */
.notification-setup {
    max-height: 80vh;
    overflow-y: auto;
}

.renewal-summary {
    background: var(--gray-50);
    padding: var(--space-4);
    border-radius: var(--radius);
    margin-bottom: var(--space-6);
    border-left: 4px solid var(--primary-color);
}

.renewal-summary h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--space-3);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-3);
}

.summary-item {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
}

.summary-item label {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.summary-item span {
    font-size: 0.9rem;
    color: var(--gray-900);
}

.urgent {
    color: var(--warning) !important;
    font-weight: 600;
}

.notification-preview {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius);
    padding: var(--space-4);
    margin-top: var(--space-4);
}

.notification-preview h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--space-3);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.preview-content {
    background: var(--white);
    border-radius: var(--radius);
    padding: var(--space-4);
    border: 1px solid var(--gray-200);
}

.preview-header {
    border-radius: var(--radius) !important;
}

.priority-badge {
    font-size: 0.75rem;
    font-weight: 700;
    padding: var(--space-1) var(--space-2);
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius);
}

.preview-details {
    margin-bottom: var(--space-3);
    padding-bottom: var(--space-3);
    border-bottom: 1px solid var(--gray-200);
}

.preview-details p {
    margin: var(--space-1) 0;
    font-size: 0.9rem;
}

.preview-message {
    font-family: monospace;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Action Button Styles */
.action-notify {
    background: var(--info);
    color: var(--white);
}

.action-notify:hover {
    background: #2563eb;
}

/* Notification Status Indicators */
.notification-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
    font-size: 0.75rem;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius);
    background: var(--info);
    color: var(--white);
    margin-left: var(--space-2);
}

.notification-indicator.sent {
    background: var(--success);
}

.notification-indicator.scheduled {
    background: var(--warning);
}

.notification-indicator.failed {
    background: var(--error);
}

/* Bulk Notification Styles */
.bulk-notification-setup {
    max-height: 80vh;
    overflow-y: auto;
}

.bulk-summary {
    background: var(--gray-50);
    padding: var(--space-4);
    border-radius: var(--radius);
    margin-bottom: var(--space-6);
    border-left: 4px solid var(--info);
}

.bulk-summary h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--space-3);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.renewal-categories {
    list-style: none;
    padding: 0;
    margin: var(--space-3) 0;
}

.renewal-categories li {
    padding: var(--space-2) 0;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.renewal-categories li:last-child {
    border-bottom: none;
}

.notification-categories {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
    margin-top: var(--space-2);
}

.category-option {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3);
    background: var(--gray-50);
    border-radius: var(--radius);
    border: 1px solid var(--gray-200);
    cursor: pointer;
    transition: all 0.2s ease;
}

.category-option:hover {
    background: var(--gray-100);
    border-color: var(--primary-color);
}

.category-option input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.category-option span {
    font-weight: 500;
    color: var(--gray-700);
}

.template-help {
    color: var(--gray-600);
    font-style: italic;
    margin-top: var(--space-2);
    display: block;
}

.preview-recipients {
    max-height: 200px;
    overflow-y: auto;
}

.preview-recipients h5 {
    margin-bottom: var(--space-3);
    color: var(--gray-800);
    font-weight: 600;
}

/* Section Actions */
.section-actions {
    display: flex;
    gap: var(--space-3);
    align-items: center;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .admin-grid {
        grid-template-columns: 1fr;
    }

    .customer-overview {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        max-height: 95vh;
    }

    .modal-header,
    .modal-body {
        padding: var(--space-4);
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .filters-container {
        padding: var(--space-4);
    }

    .filter-group {
        flex-direction: column;
        align-items: stretch;
    }

    .customer-controls {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-3);
    }

    .customer-list.list-view .customer-item {
        grid-template-columns: 1fr;
        gap: var(--space-2);
        text-align: center;
    }

    .table-responsive {
        font-size: 0.8rem;
    }

    .data-table th,
    .data-table td {
        padding: var(--space-2);
    }

    .table-footer {
        flex-direction: column;
        gap: var(--space-3);
        align-items: stretch;
    }

    .admin-actions {
        grid-template-columns: 1fr;
    }

    .toast-container {
        top: var(--space-4);
        right: var(--space-4);
        left: var(--space-4);
    }

    .toast {
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .customer-overview {
        gap: var(--space-4);
    }

    .customer-type-card {
        flex-direction: column;
        text-align: center;
        gap: var(--space-3);
    }

    .card-arrow {
        display: none;
    }

    .action-btn {
        padding: var(--space-1) var(--space-2);
        font-size: 0.7rem;
    }

    .renewal-type-overview {
        grid-template-columns: 1fr;
    }

    .detail-grid {
        grid-template-columns: 1fr;
    }

    .modal-actions {
        flex-direction: column;
    }
}
