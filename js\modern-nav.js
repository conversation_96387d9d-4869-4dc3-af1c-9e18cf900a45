// Modern Navigation JavaScript

// Global variables
let activeDropdown = null;
let searchTimeout = null;

// Initialize navigation when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeModernNav();
});

function initializeModernNav() {
    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown') && !e.target.closest('.search-container')) {
            closeAllDropdowns();
            clearSearchResults();
        }
    });

    // Close dropdowns on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeAllDropdowns();
            closeSearch();
            closeMobileNav();
            clearSearchResults();
        }
    });

    // Initialize navigation items
    initializeNavItems();
    
    // Update user info
    updateUserInfo();
    
    // Update platform badge
    updatePlatformBadge();
}

// Navigation item management
function initializeNavItems() {
    const navItems = document.querySelectorAll('.nav-item, .mobile-nav-item');
    
    navItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all items
            navItems.forEach(nav => nav.classList.remove('active'));
            
            // Add active class to clicked item and corresponding items
            const href = this.getAttribute('href');
            document.querySelectorAll(`[href="${href}"]`).forEach(nav => {
                nav.classList.add('active');
            });
            
            // Update breadcrumb
            updateBreadcrumb(href);
            
            // Close mobile nav if open
            closeMobileNav();
        });
    });
}

// Dropdown management
function toggleNotifications() {
    toggleDropdown('notificationsMenu');
}

function toggleQuickActions() {
    toggleDropdown('quickActionsMenu');
}

function toggleUserMenu() {
    toggleDropdown('userMenu');
}

function toggleDropdown(menuId) {
    const menu = document.getElementById(menuId);
    const overlay = document.getElementById('navOverlay');
    
    // Close other dropdowns first
    if (activeDropdown && activeDropdown !== menuId) {
        closeAllDropdowns();
    }
    
    if (menu.classList.contains('show')) {
        menu.classList.remove('show');
        overlay.classList.remove('show');
        activeDropdown = null;
    } else {
        menu.classList.add('show');
        overlay.classList.add('show');
        activeDropdown = menuId;
    }
}

function closeAllDropdowns() {
    const dropdowns = document.querySelectorAll('.dropdown-menu');
    const overlay = document.getElementById('navOverlay');
    
    dropdowns.forEach(dropdown => {
        dropdown.classList.remove('show');
    });
    
    overlay.classList.remove('show');
    activeDropdown = null;
}

// Search functionality
function toggleSearch() {
    const searchBox = document.getElementById('globalSearch');
    const overlay = document.getElementById('navOverlay');
    
    if (searchBox.classList.contains('active')) {
        closeSearch();
    } else {
        closeAllDropdowns();
        searchBox.classList.add('active');
        overlay.classList.add('show');
        searchBox.querySelector('input').focus();
    }
}

function closeSearch() {
    const searchBox = document.getElementById('globalSearch');
    const overlay = document.getElementById('navOverlay');
    
    searchBox.classList.remove('active');
    overlay.classList.remove('show');
    searchBox.querySelector('input').value = '';
    clearSearchResults();
}

function globalSearch(event) {
    const query = event.target.value.toLowerCase().trim();
    
    // Clear previous timeout
    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }
    
    // Clear results if query is too short
    if (query.length < 2) {
        clearSearchResults();
        return;
    }
    
    // Debounce search
    searchTimeout = setTimeout(() => {
        if (query.length >= 2) {
            performGlobalSearch(query);
        }
    }, 300);
    
    // Handle Enter key
    if (event.key === 'Enter' && query.length >= 2) {
        performGlobalSearch(query);
    }
}

function performGlobalSearch(query) {
    console.log('Searching for:', query);
    
    // Search across all data types
    const results = {
        purchaseOrders: searchPurchaseOrders(query),
        customers: searchCustomersData(query),
        renewals: searchRenewalsData(query),
        users: searchUsersData(query)
    };
    
    // Show search results
    showSearchResults(results, query);
}

function searchPurchaseOrders(query) {
    try {
        const pos = getPurchaseOrders();
        return pos.filter(po => {
            return po.id.toLowerCase().includes(query) ||
                   po.customerName.toLowerCase().includes(query) ||
                   po.description.toLowerCase().includes(query) ||
                   po.status.toLowerCase().includes(query) ||
                   po.amount.toString().includes(query);
        }).map(po => ({
            ...po,
            type: 'Purchase Order',
            icon: 'fas fa-shopping-cart',
            displayName: po.id,
            subtitle: `${po.customerName} • $${po.amount.toLocaleString()} • ${po.status}`
        }));
    } catch (error) {
        console.error('Error searching purchase orders:', error);
        return [];
    }
}

function searchCustomersData(query) {
    try {
        const customers = getCustomers();
        return customers.filter(customer => {
            return customer.name.toLowerCase().includes(query) ||
                   customer.email.toLowerCase().includes(query) ||
                   customer.phone.toLowerCase().includes(query) ||
                   customer.type.toLowerCase().includes(query) ||
                   customer.company.toLowerCase().includes(query);
        }).map(customer => ({
            ...customer,
            type: 'Customer',
            icon: 'fas fa-user',
            displayName: customer.name,
            subtitle: `${customer.email} • ${customer.type} • ${customer.company}`
        }));
    } catch (error) {
        console.error('Error searching customers:', error);
        return [];
    }
}

function searchRenewalsData(query) {
    try {
        const renewals = getRenewals();
        return renewals.filter(renewal => {
            return renewal.id.toLowerCase().includes(query) ||
                   renewal.customerName.toLowerCase().includes(query) ||
                   renewal.description.toLowerCase().includes(query) ||
                   renewal.renewalType.toLowerCase().includes(query) ||
                   renewal.status.toLowerCase().includes(query) ||
                   renewal.amount.toString().includes(query);
        }).map(renewal => ({
            ...renewal,
            type: 'Renewal',
            icon: 'fas fa-sync-alt',
            displayName: renewal.id,
            subtitle: `${renewal.customerName} • $${renewal.amount.toLocaleString()} • ${renewal.status}`
        }));
    } catch (error) {
        console.error('Error searching renewals:', error);
        return [];
    }
}

function searchUsersData(query) {
    try {
        const users = getUsers();
        return users.filter(user => {
            return user.name.toLowerCase().includes(query) ||
                   user.email.toLowerCase().includes(query) ||
                   user.role.toLowerCase().includes(query);
        }).map(user => ({
            ...user,
            type: 'User',
            icon: 'fas fa-user-circle',
            displayName: user.name,
            subtitle: `${user.email} • ${user.role}`
        }));
    } catch (error) {
        console.error('Error searching users:', error);
        return [];
    }
}

function showSearchResults(results, query) {
    // Combine all results
    const allResults = [
        ...results.purchaseOrders,
        ...results.customers,
        ...results.renewals,
        ...results.users
    ];

    if (allResults.length === 0) {
        showSearchDropdown([], query);
        return;
    }

    // Sort by relevance (exact matches first)
    allResults.sort((a, b) => {
        const aExact = a.displayName?.toLowerCase() === query || a.id?.toLowerCase() === query;
        const bExact = b.displayName?.toLowerCase() === query || b.id?.toLowerCase() === query;

        if (aExact && !bExact) return -1;
        if (!aExact && bExact) return 1;
        return 0;
    });

    // Limit to top 10 results
    const topResults = allResults.slice(0, 10);

    showSearchDropdown(topResults, query);
}

function showSearchDropdown(results, query) {
    // Remove existing search results
    clearSearchResults();

    // Create search results dropdown
    const searchContainer = document.querySelector('.search-container');
    const dropdown = document.createElement('div');
    dropdown.id = 'searchResults';
    dropdown.className = 'search-results-dropdown';

    if (results.length === 0) {
        dropdown.innerHTML = `
            <div class="search-no-results">
                <i class="fas fa-search"></i>
                <p>No results found for "${query}"</p>
                <small>Try searching for purchase orders, customers, renewals, or users</small>
            </div>
        `;
    } else {
        dropdown.innerHTML = `
            <div class="search-results-header">
                <span>Search Results (${results.length})</span>
                <button onclick="clearSearchResults()" class="search-clear">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="search-results-list">
                ${results.map((result, index) => `
                    <div class="search-result-item" onclick="selectSearchResult('${result.type}', '${result.id}', ${index})">
                        <div class="search-result-icon">
                            <i class="${result.icon}"></i>
                        </div>
                        <div class="search-result-content">
                            <div class="search-result-title">
                                ${highlightSearchTerm(result.displayName, query)}
                            </div>
                            <div class="search-result-subtitle">
                                ${result.subtitle}
                            </div>
                        </div>
                        <div class="search-result-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                `).join('')}
            </div>
            ${results.length >= 10 ? `
                <div class="search-results-footer">
                    <button onclick="showAllSearchResults('${query}')" class="search-view-all">
                        View all results for "${query}"
                    </button>
                </div>
            ` : ''}
        `;
    }

    searchContainer.appendChild(dropdown);

    // Show the dropdown
    dropdown.style.display = 'block';
}

function highlightSearchTerm(text, term) {
    if (!text || !term) return text;

    const regex = new RegExp(`(${term})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
}

function selectSearchResult(type, id, index) {
    console.log('Selected search result:', type, id);

    // Navigate to appropriate section
    switch (type) {
        case 'Purchase Order':
            showSection('purchase-orders');
            break;
        case 'Customer':
            showSection('customers');
            break;
        case 'Renewal':
            showSection('renewals');
            break;
        case 'User':
            showSection('admin');
            break;
    }

    // Close search
    closeSearch();

    // Show notification about navigation
    showNotification(`Navigated to ${type}: ${id}`, 'success');
}

function clearSearchResults() {
    const dropdown = document.getElementById('searchResults');
    if (dropdown) {
        dropdown.remove();
    }
}

function showAllSearchResults(query) {
    // Implementation for showing all search results in a dedicated page/modal
    closeSearch();
    clearSearchResults();
    showNotification(`Showing all results for "${query}"`, 'info');
}

// Mobile navigation
function toggleMobileNav() {
    const mobileNav = document.getElementById('mobileNav');
    const overlay = document.getElementById('navOverlay');

    if (mobileNav && mobileNav.classList.contains('show')) {
        closeMobileNav();
    } else if (mobileNav) {
        closeAllDropdowns();
        closeSearch();
        mobileNav.classList.add('show');
        overlay.classList.add('show');
    }
}

function closeMobileNav() {
    const mobileNav = document.getElementById('mobileNav');
    const overlay = document.getElementById('navOverlay');

    if (mobileNav) {
        mobileNav.classList.remove('show');
    }
    if (overlay) {
        overlay.classList.remove('show');
    }
}

// Breadcrumb management
function updateBreadcrumb(section) {
    const breadcrumb = document.getElementById('currentBreadcrumb');
    const pageActions = document.getElementById('pageActions');

    if (!breadcrumb || !pageActions) return;

    const sectionNames = {
        '#dashboard': 'Dashboard',
        '#purchase-orders': 'Purchase Orders',
        '#customers': 'Customers',
        '#renewals': 'Renewals',
        '#admin': 'Admin Panel'
    };

    const sectionActions = {
        '#dashboard': '',
        '#purchase-orders': `
            <button class="btn btn-primary" onclick="createNewPO()">
                <i class="fas fa-plus"></i> New PO
            </button>
        `,
        '#customers': `
            <button class="btn btn-primary" onclick="createNewCustomer()">
                <i class="fas fa-user-plus"></i> Add Customer
            </button>
        `,
        '#renewals': `
            <button class="btn btn-outline" onclick="sendBulkNotifications()">
                <i class="fas fa-bullhorn"></i> Bulk Notifications
            </button>
            <button class="btn btn-primary" onclick="createNewRenewal()">
                <i class="fas fa-plus"></i> New Renewal
            </button>
        `,
        '#admin': ''
    };

    breadcrumb.textContent = sectionNames[section] || 'Dashboard';
    pageActions.innerHTML = sectionActions[section] || '';
}

// User info management
function updateUserInfo() {
    try {
        const currentUser = getCurrentUser();
        if (currentUser) {
            // Update all user name elements
            const userNameElements = document.querySelectorAll('#currentUserName, .user-name');
            const userRoleElements = document.querySelectorAll('#currentUserRole, .user-role');

            userNameElements.forEach(el => {
                if (el) el.textContent = currentUser.name;
            });

            userRoleElements.forEach(el => {
                if (el) el.textContent = currentUser.role;
            });
        }
    } catch (error) {
        console.error('Error updating user info:', error);
    }
}

// Platform badge management
function updatePlatformBadge() {
    try {
        const currentUser = getCurrentUser();
        if (currentUser) {
            const badges = document.querySelectorAll('#platformBadge, .platform-badge');
            badges.forEach(badge => {
                badge.textContent = currentUser.platform.toUpperCase();
                badge.className = `platform-badge ${currentUser.platform.toLowerCase()}`;
            });
        }
    } catch (error) {
        console.error('Error updating platform badge:', error);
    }
}

// Notification management
function markAllRead() {
    const notifications = document.querySelectorAll('.notification-item.unread');
    notifications.forEach(notification => {
        notification.classList.remove('unread');
    });

    // Update notification badge
    const badge = document.querySelector('.notification-badge');
    if (badge) {
        badge.textContent = '0';
        badge.style.display = 'none';
    }

    showNotification('All notifications marked as read', 'success');
}

function viewAllNotifications() {
    closeAllDropdowns();
    showNotification('Opening notifications page...', 'info');
}

// Quick action functions
function createNewCustomer() {
    closeAllDropdowns();
    if (typeof addCustomer === 'function') {
        addCustomer();
    } else {
        showNotification('Add customer functionality not available', 'warning');
    }
}

function exportData() {
    closeAllDropdowns();
    showNotification('Exporting data...', 'info');
}

function importData() {
    closeAllDropdowns();
    showNotification('Import data functionality coming soon', 'info');
}

// Profile and preferences
function showProfile() {
    closeAllDropdowns();
    showNotification('Profile settings coming soon', 'info');
}

function showPreferences() {
    closeAllDropdowns();
    showNotification('Preferences coming soon', 'info');
}

function showHelp() {
    closeAllDropdowns();
    showNotification('Help & Support coming soon', 'info');
}

// Utility functions
function showNotifications() {
    toggleNotifications();
}

// Export functions for global access
window.toggleNotifications = toggleNotifications;
window.toggleQuickActions = toggleQuickActions;
window.toggleUserMenu = toggleUserMenu;
window.toggleSearch = toggleSearch;
window.closeSearch = closeSearch;
window.globalSearch = globalSearch;
window.toggleMobileNav = toggleMobileNav;
window.closeMobileNav = closeMobileNav;
window.closeAllDropdowns = closeAllDropdowns;
window.clearSearchResults = clearSearchResults;
window.selectSearchResult = selectSearchResult;
window.showAllSearchResults = showAllSearchResults;
window.markAllRead = markAllRead;
window.viewAllNotifications = viewAllNotifications;
window.createNewCustomer = createNewCustomer;
window.exportData = exportData;
window.importData = importData;
window.showProfile = showProfile;
window.showPreferences = showPreferences;
window.showHelp = showHelp;
window.showNotifications = showNotifications;

// Basic section management for demo
function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });

    // Show target section
    const targetSection = document.getElementById(sectionId.replace('#', ''));
    if (targetSection) {
        targetSection.classList.add('active');
    }

    // Update navigation
    updateBreadcrumb('#' + sectionId.replace('#', ''));
}

// Sample data for search functionality
function getPurchaseOrders() {
    return [
        { id: 'PO-2024-001', customerName: 'Acme Corp', description: 'Software License', status: 'Approved', amount: 15000 },
        { id: 'PO-2024-002', customerName: 'Tech Solutions', description: 'Support Services', status: 'Pending', amount: 8500 },
        { id: 'PO-2024-003', customerName: 'Global Industries', description: 'Hardware Purchase', status: 'Draft', amount: 25000 },
        { id: 'PO-2024-004', customerName: 'Innovation Labs', description: 'Consulting Services', status: 'Approved', amount: 12000 },
        { id: 'PO-2024-005', customerName: 'Digital Dynamics', description: 'Cloud Migration', status: 'Pending', amount: 35000 }
    ];
}

function getCustomers() {
    return [
        { id: 'CUST-001', name: 'John Smith', email: '<EMAIL>', phone: '******-0101', type: 'EE', company: 'Acme Corp' },
        { id: 'CUST-002', name: 'Sarah Johnson', email: '<EMAIL>', phone: '******-0102', type: 'EN', company: 'Tech Solutions' },
        { id: 'CUST-003', name: 'Mike Wilson', email: '<EMAIL>', phone: '******-0103', type: 'NN', company: 'Global Industries' },
        { id: 'CUST-004', name: 'Lisa Chen', email: '<EMAIL>', phone: '******-0104', type: 'EE', company: 'Innovation Labs' },
        { id: 'CUST-005', name: 'David Brown', email: '<EMAIL>', phone: '******-0105', type: 'EN', company: 'Digital Dynamics' }
    ];
}

function getRenewals() {
    return [
        { id: 'REN-2024-001', customerName: 'Acme Corp', description: 'License Renewal', renewalType: 'License', status: 'Active', amount: 18000 },
        { id: 'REN-2024-002', customerName: 'Tech Solutions', description: 'Support Renewal', renewalType: 'Support Activity', status: 'Pending', amount: 9500 },
        { id: 'REN-2024-003', customerName: 'Global Industries', description: 'Change Request', renewalType: 'CR', status: 'In Progress', amount: 5000 },
        { id: 'REN-2024-004', customerName: 'Innovation Labs', description: 'License Extension', renewalType: 'License', status: 'Active', amount: 22000 },
        { id: 'REN-2024-005', customerName: 'Digital Dynamics', description: 'Support Package', renewalType: 'Support Activity', status: 'Expired', amount: 15000 }
    ];
}

function getUsers() {
    return [
        { id: 'USER-001', name: 'John Admin', email: '<EMAIL>', role: 'Admin' },
        { id: 'USER-002', name: 'Sarah Manager', email: '<EMAIL>', role: 'Manager' },
        { id: 'USER-003', name: 'Mike User', email: '<EMAIL>', role: 'User' },
        { id: 'USER-004', name: 'Lisa Viewer', email: '<EMAIL>', role: 'Read-Only' }
    ];
}

function getCurrentUser() {
    return {
        name: 'John Admin',
        email: '<EMAIL>',
        role: 'Admin',
        platform: 'platinum'
    };
}

// Basic notification system
function showNotification(message, type = 'info') {
    console.log(`${type.toUpperCase()}: ${message}`);

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : type === 'error' ? 'times-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;

    // Add to container
    let container = document.getElementById('notificationContainer');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notificationContainer';
        container.className = 'notification-container';
        document.body.appendChild(container);
    }

    container.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

// Export additional functions
window.showSection = showSection;
window.getPurchaseOrders = getPurchaseOrders;
window.getCustomers = getCustomers;
window.getRenewals = getRenewals;
window.getUsers = getUsers;
window.getCurrentUser = getCurrentUser;
window.showNotification = showNotification;
