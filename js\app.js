// Enhanced Main Application Logic

// Global variables
let currentSection = 'dashboard';
let currentPage = 1;
let itemsPerPage = 10;
let filteredData = [];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Initialize navigation
    initializeNavigation();

    // Initialize dashboard
    initializeDashboard();

    // Initialize modal functionality
    initializeModal();

    // Initialize mobile menu
    initializeMobileMenu();

    // Show dashboard by default
    showSection('dashboard');

    // Update page title
    updatePageTitle('Dashboard', 'Welcome to your HCL Software portal');
}

// Mobile menu functionality
function initializeMobileMenu() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobileOverlay');

    if (mobileToggle) {
        mobileToggle.addEventListener('click', toggleMobileMenu);
    }

    if (overlay) {
        overlay.addEventListener('click', toggleMobileMenu);
    }
}

function toggleMobileMenu() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobileOverlay');

    if (sidebar && overlay) {
        sidebar.classList.toggle('open');
        overlay.classList.toggle('show');
    }
}

// Update page title and subtitle
function updatePageTitle(title, subtitle) {
    const pageTitle = document.getElementById('pageTitle');
    const pageSubtitle = document.getElementById('pageSubtitle');

    if (pageTitle) pageTitle.textContent = title;
    if (pageSubtitle) pageSubtitle.textContent = subtitle;
}

// Enhanced Navigation functionality
function initializeNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const section = this.getAttribute('href').substring(1);
            showSection(section);

            // Close mobile menu if open
            if (window.innerWidth <= 768) {
                toggleMobileMenu();
            }
        });
    });
}

// Enhanced section management
function showSection(sectionName) {
    // Hide all sections
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => {
        section.classList.remove('active');
    });

    // Show target section
    const targetSection = document.getElementById(sectionName);
    if (targetSection) {
        targetSection.classList.add('active');
        currentSection = sectionName;

        // Update navigation
        updateNavigation(sectionName);

        // Update page title
        updatePageTitleForSection(sectionName);

        // Load section-specific content
        loadSectionContent(sectionName);

        // Reset pagination
        currentPage = 1;
    }
}

// Update page title based on section
function updatePageTitleForSection(sectionName) {
    const titles = {
        'dashboard': {
            title: 'Dashboard',
            subtitle: 'Welcome to your HCL Software portal'
        },
        'purchase-orders': {
            title: 'Purchase Orders',
            subtitle: 'Manage and track all purchase orders'
        },
        'customers': {
            title: 'Customer Management',
            subtitle: 'Manage your customer relationships and data'
        },
        'renewals': {
            title: 'Renewal Management',
            subtitle: 'Track and manage customer renewals across all types'
        },
        'admin': {
            title: 'Admin Panel',
            subtitle: 'System administration and configuration'
        }
    };

    const sectionInfo = titles[sectionName] || { title: 'Portal', subtitle: 'HCL Software' };
    updatePageTitle(sectionInfo.title, sectionInfo.subtitle);
}

// Update navigation active state
function updateNavigation(activeSection) {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${activeSection}`) {
            link.classList.add('active');
        }
    });
}

// Load content for specific section
function loadSectionContent(sectionName) {
    switch (sectionName) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'purchase-orders':
            loadPurchaseOrders();
            break;
        case 'customers':
            loadCustomers();
            break;
        case 'renewals':
            loadRenewals();
            break;
        case 'admin':
            loadAdmin();
            break;
    }
}

// Dashboard functionality
function initializeDashboard() {
    loadDashboard();
}

function loadDashboard() {
    const stats = getStats();
    const customerStats = getCustomerStats();

    // Update statistics with enhanced data
    updateElement('pendingPOs', stats.pendingPOs);
    updateElement('activeCustomers', stats.activeCustomers);
    updateElement('monthlyOrders', stats.monthlyOrders);
    updateElement('totalRevenue', `$${(stats.totalRevenue / 1000).toFixed(0)}K`);

    // Update pending approval amount display
    updateElement('pendingAmount', `$${stats.pendingAmount.toLocaleString()} pending approval`);

    // Update customer counts
    updateElement('nnCount', customerStats.NN);
    updateElement('enCount', customerStats.EN);
    updateElement('eeCount', customerStats.EE);

    // Load timeline data
    loadTimelineData();

    // Load recent purchase orders
    loadRecentPOs();

    // Load platform features
    loadPlatformFeatures();

    // Load customer distribution
    loadCustomerDistribution();

    // Initialize dashboard charts
    setTimeout(() => {
        if (typeof initializeDashboardChart === 'function') {
            initializeDashboardChart();
        }
    }, 100);

    // Update user name in sidebar
    const user = getCurrentUser();
    if (user) {
        updateElement('userName', user.name);
    }
}

function loadCustomerDistribution() {
    const customerStats = getCustomerStats();
    const container = document.getElementById('customerDistribution');

    if (!container) return;

    const total = customerStats.NN + customerStats.EN + customerStats.EE;

    container.innerHTML = `
        <div class="distribution-item">
            <div class="distribution-label">
                <i class="fas fa-user-plus" style="color: #3b82f6;"></i>
                <span>NN Customers</span>
            </div>
            <div class="distribution-count">${customerStats.NN}</div>
        </div>
        <div class="distribution-item">
            <div class="distribution-label">
                <i class="fas fa-user-check" style="color: #8b5cf6;"></i>
                <span>EN Customers</span>
            </div>
            <div class="distribution-count">${customerStats.EN}</div>
        </div>
        <div class="distribution-item">
            <div class="distribution-label">
                <i class="fas fa-building" style="color: #f59e0b;"></i>
                <span>EE Customers</span>
            </div>
            <div class="distribution-count">${customerStats.EE}</div>
        </div>
        <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid var(--gray-200); text-align: center;">
            <strong>Total: ${total} customers</strong>
        </div>
    `;
}

function loadRecentPOs() {
    const recentPOs = getPurchaseOrders()
        .sort((a, b) => new Date(b.created) - new Date(a.created))
        .slice(0, 5);

    const container = document.getElementById('recentPOs');
    if (!container) return;

    container.innerHTML = recentPOs.map(po => `
        <div class="po-item-summary" onclick="viewPO('${po.id}')">
            <div>
                <div class="po-number">${po.id}</div>
                <div>${po.customerName}</div>
            </div>
            <div>
                <div class="po-amount" style="font-weight: ${po.status === 'pending' ? 'bold' : 'normal'}; color: ${po.status === 'pending' ? '#d97706' : 'inherit'};">
                    $${po.amount.toLocaleString()}
                </div>
                <div class="status-badge status-${po.status}">${PO_STATUSES[po.status]}</div>
            </div>
        </div>
    `).join('');
}

function loadPlatformFeatures() {
    const user = getCurrentUser();
    if (!user) return;
    
    const platform = PLATFORMS[user.platform];
    const container = document.getElementById('platformFeatures');
    
    if (!container) return;
    
    container.innerHTML = `
        <ul class="platform-features">
            ${platform.features.map(feature => `<li>${feature}</li>`).join('')}
        </ul>
        <div style="margin-top: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 5px;">
            <strong>Platform Limits:</strong><br>
            Max Users: ${platform.maxUsers === -1 ? 'Unlimited' : platform.maxUsers}<br>
            Max POs: ${platform.maxPOs === -1 ? 'Unlimited' : platform.maxPOs}<br>
            Approval Levels: ${platform.approvalLevels}
        </div>
    `;
}

// Purchase Orders functionality
function loadPurchaseOrders() {
    const pos = getPurchaseOrders();
    const tbody = document.getElementById('poTableBody');

    if (!tbody) return;

    tbody.innerHTML = pos.map(po => `
        <tr ${po.status === 'pending' ? 'style="background-color: #fef3c7; border-left: 4px solid #f59e0b;"' : ''}>
            <td>${po.id}</td>
            <td>${po.customerName}</td>
            <td class="customer-type-badge customer-type-${po.customerType.toLowerCase()}">${po.customerType}</td>
            <td style="font-weight: ${po.status === 'pending' ? 'bold' : 'normal'}; color: ${po.status === 'pending' ? '#d97706' : 'inherit'};">
                $${po.amount.toLocaleString()}
                ${po.status === 'pending' ? '<br><small style="color: #92400e;">⏳ Awaiting Approval</small>' : ''}
            </td>
            <td><span class="status-badge status-${po.status}">${PO_STATUSES[po.status]}</span></td>
            <td>${formatDate(po.created)}</td>
            <td>
                <button class="action-btn action-view" onclick="viewPO('${po.id}')">View</button>
                ${auth.canWrite() ? `<button class="action-btn action-edit" onclick="editPO('${po.id}')">Edit</button>` : ''}
                ${auth.canApprove() && po.status === 'pending' ? `
                    <button class="action-btn action-approve" onclick="approvePO('${po.id}')">Approve</button>
                    <button class="action-btn action-reject" onclick="rejectPO('${po.id}')">Reject</button>
                ` : ''}
            </td>
        </tr>
    `).join('');

    // Initialize charts when purchase orders section is loaded
    setTimeout(() => {
        if (typeof initializePOCharts === 'function') {
            initializePOCharts();
        }
    }, 100);
}

// Customer functionality
function loadCustomers() {
    const customers = getCustomers();
    const container = document.getElementById('customerList');
    
    if (!container) return;
    
    container.innerHTML = `
        <div class="customer-list">
            ${customers.map(customer => `
                <div class="customer-item">
                    <div>${customer.name}</div>
                    <div>${customer.email}</div>
                    <div class="customer-type-badge customer-type-${customer.type.toLowerCase()}">${customer.type}</div>
                    <div>${customer.renewalTypes.length > 0 ? customer.renewalTypes.join(', ') : 'N/A'}</div>
                    <div>
                        <button class="action-btn action-view" onclick="viewCustomer(${customer.id})">View</button>
                        ${auth.canWrite() ? `<button class="action-btn action-edit" onclick="editCustomer(${customer.id})">Edit</button>` : ''}
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

// Admin functionality
function loadAdmin() {
    if (!auth.isAdmin()) {
        showNotification('Access denied. Admin privileges required.', 'error');
        showSection('dashboard');
        return;
    }
    
    const userStats = getUserStats();
    
    // Update user counts
    updateElement('adminCount', userStats.admin);
    updateElement('readCount', userStats.read);
    updateElement('readWriteCount', userStats.readwrite);
}

// Modal functionality
function initializeModal() {
    const modal = document.getElementById('modal');
    
    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });
    
    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeModal();
        }
    });
}

function showModal() {
    const modal = document.getElementById('modal');
    modal.classList.add('show');
}

function closeModal() {
    const modal = document.getElementById('modal');
    modal.classList.remove('show');
}

// Utility functions
function updateElement(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = value;
    }
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString();
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

// Enhanced Toast Notification System
function showNotification(message, type = 'info', duration = 5000) {
    const container = document.getElementById('toastContainer') || createToastContainer();

    const toast = document.createElement('div');
    toast.className = `toast ${type}`;

    const icon = getNotificationIcon(type);

    toast.innerHTML = `
        <div style="display: flex; align-items: center; gap: 0.75rem;">
            <i class="${icon}" style="font-size: 1.2rem;"></i>
            <span style="flex: 1;">${message}</span>
            <button onclick="removeToast(this)" style="background: none; border: none; color: inherit; cursor: pointer; padding: 0.25rem;">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    container.appendChild(toast);

    // Auto remove after duration
    setTimeout(() => {
        removeToast(toast);
    }, duration);

    // Animate in
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
        toast.style.opacity = '1';
    }, 10);
}

function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toastContainer';
    container.className = 'toast-container';
    document.body.appendChild(container);
    return container;
}

function getNotificationIcon(type) {
    const icons = {
        'success': 'fas fa-check-circle',
        'error': 'fas fa-exclamation-circle',
        'warning': 'fas fa-exclamation-triangle',
        'info': 'fas fa-info-circle'
    };
    return icons[type] || icons.info;
}

function removeToast(element) {
    if (element && element.parentNode) {
        element.style.transform = 'translateX(100%)';
        element.style.opacity = '0';
        setTimeout(() => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
        }, 300);
    }
}

// Loading spinner functions
function showLoading() {
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
        spinner.classList.add('show');
    }
}

function hideLoading() {
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
        spinner.classList.remove('show');
    }
}

// Search functionality for Purchase Orders
function searchPOs() {
    const searchTerm = document.getElementById('poSearch').value.toLowerCase();
    const allPOs = getPurchaseOrders();

    filteredData = allPOs.filter(po =>
        po.id.toLowerCase().includes(searchTerm) ||
        po.customerName.toLowerCase().includes(searchTerm) ||
        po.status.toLowerCase().includes(searchTerm)
    );

    currentPage = 1;
    updatePOTable();
}

// Update PO table with pagination and filtering
function updatePOTable() {
    const tbody = document.getElementById('poTableBody');
    if (!tbody) return;

    const dataToShow = filteredData.length > 0 ? filteredData : getPurchaseOrders();
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedData = dataToShow.slice(startIndex, endIndex);

    tbody.innerHTML = paginatedData.map(po => `
        <tr ${po.status === 'pending' ? 'style="background-color: #fef3c7; border-left: 4px solid #f59e0b;"' : ''}>
            <td>${po.id}</td>
            <td>${po.customerName}</td>
            <td class="customer-type-badge customer-type-${po.customerType.toLowerCase()}">${po.customerType}</td>
            <td style="font-weight: ${po.status === 'pending' ? 'bold' : 'normal'}; color: ${po.status === 'pending' ? '#d97706' : 'inherit'};">
                $${po.amount.toLocaleString()}
                ${po.status === 'pending' ? '<br><small style="color: #92400e;">⏳ Awaiting Approval</small>' : ''}
            </td>
            <td><span class="status-badge status-${po.status}">${PO_STATUSES[po.status]}</span></td>
            <td>${formatDate(po.created)}</td>
            <td>
                <button class="action-btn action-view" onclick="viewPO('${po.id}')">View</button>
                ${auth.canWrite() ? `<button class="action-btn action-edit" onclick="editPO('${po.id}')">Edit</button>` : ''}
                ${auth.canApprove() && po.status === 'pending' ? `
                    <button class="action-btn action-approve" onclick="approvePO('${po.id}')">Approve</button>
                    <button class="action-btn action-reject" onclick="rejectPO('${po.id}')">Reject</button>
                ` : ''}
            </td>
        </tr>
    `).join('');

    // Update pagination info
    updatePaginationInfo(dataToShow.length);
}

// Update pagination information
function updatePaginationInfo(totalItems) {
    const tableInfo = document.getElementById('tableInfo');
    const pageInfo = document.getElementById('pageInfo');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');

    if (tableInfo) {
        const startIndex = (currentPage - 1) * itemsPerPage + 1;
        const endIndex = Math.min(currentPage * itemsPerPage, totalItems);
        tableInfo.textContent = totalItems > 0 ?
            `Showing ${startIndex}-${endIndex} of ${totalItems} purchase orders` :
            'Showing 0 of 0 purchase orders';
    }

    const totalPages = Math.ceil(totalItems / itemsPerPage);
    if (pageInfo) {
        pageInfo.textContent = `Page ${currentPage} of ${Math.max(1, totalPages)}`;
    }

    if (prevBtn) prevBtn.disabled = currentPage <= 1;
    if (nextBtn) nextBtn.disabled = currentPage >= totalPages;
}

// Search functionality for Customers
function searchCustomers() {
    const searchTerm = document.getElementById('customerSearch').value.toLowerCase();
    const allCustomers = getCustomers();

    const filtered = allCustomers.filter(customer =>
        customer.name.toLowerCase().includes(searchTerm) ||
        customer.email.toLowerCase().includes(searchTerm) ||
        customer.type.toLowerCase().includes(searchTerm)
    );

    displayFilteredCustomers(filtered);
}

// Clear filters
function clearFilters() {
    document.getElementById('statusFilter').value = '';
    document.getElementById('customerFilter').value = '';
    if (document.getElementById('poSearch')) {
        document.getElementById('poSearch').value = '';
    }

    filteredData = [];
    currentPage = 1;

    if (currentSection === 'purchase-orders') {
        loadPurchaseOrders();
    }
}

// Pagination functions
function previousPage() {
    if (currentPage > 1) {
        currentPage--;
        updatePOTable();
    }
}

function nextPage() {
    const totalPages = Math.ceil(filteredData.length / itemsPerPage);
    if (currentPage < totalPages) {
        currentPage++;
        updatePOTable();
    }
}

// Export functionality
function exportPOs() {
    showNotification('Export functionality would be implemented here', 'info');
}

function exportData() {
    showNotification('Data export started', 'success');
}

// Admin functions
function systemBackup() {
    showNotification('System backup initiated', 'success');
}

function viewLogs() {
    showNotification('System logs would be displayed here', 'info');
}

function systemHealth() {
    showNotification('System health: All systems operational', 'success');
}

// Notification and help functions
function showNotifications() {
    showNotification('You have 3 new notifications', 'info');
}

function showHelp() {
    showNotification('Help documentation would open here', 'info');
}

// Timeline Data Functions
function loadTimelineData() {
    const timelineData = getTimelineStats();

    // Update Today
    updateTimelineCard('today', timelineData.today, 5);

    // Update Weekly
    updateTimelineCard('weekly', timelineData.weekly, 25);

    // Update MTD
    updateTimelineCard('mtd', timelineData.mtd, 100);

    // Update YTD
    updateTimelineCard('ytd', timelineData.ytd, 1200);
}

function updateTimelineCard(period, actual, target) {
    const percentage = Math.min((actual / target) * 100, 100);

    // Update numbers
    updateElement(`${period}POs`, actual);
    updateElement(`${period}Target`, target);
    updateElement(`${period}Percentage`, Math.round(percentage));

    // Update progress bar with animation
    const progressBar = document.getElementById(`${period}ProgressBar`);
    if (progressBar) {
        // Reset width first
        progressBar.style.width = '0%';

        // Animate to target width
        setTimeout(() => {
            progressBar.style.width = `${percentage}%`;
        }, 100);
    }
}

function getTimelineStats() {
    const pos = getPurchaseOrders();
    const now = new Date();

    // Get today's date boundaries
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000);

    // Get week boundaries (Monday to Sunday)
    const weekStart = new Date(now);
    const dayOfWeek = now.getDay();
    const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
    weekStart.setDate(now.getDate() - daysToMonday);
    weekStart.setHours(0, 0, 0, 0);

    // Get month boundaries
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

    // Get year boundaries
    const yearStart = new Date(now.getFullYear(), 0, 1);

    // Count POs for each period
    const today = pos.filter(po => {
        const poDate = new Date(po.created);
        return poDate >= todayStart && poDate < todayEnd;
    }).length;

    const weekly = pos.filter(po => {
        const poDate = new Date(po.created);
        return poDate >= weekStart;
    }).length;

    const mtd = pos.filter(po => {
        const poDate = new Date(po.created);
        return poDate >= monthStart;
    }).length;

    const ytd = pos.filter(po => {
        const poDate = new Date(po.created);
        return poDate >= yearStart;
    }).length;

    return {
        today: today,
        weekly: weekly,
        mtd: mtd,
        ytd: ytd
    };
}

// Filter functionality for Purchase Orders
function filterPOs() {
    const statusFilter = document.getElementById('statusFilter').value;
    const customerFilter = document.getElementById('customerFilter').value;
    
    let filteredPOs = getPurchaseOrders();
    
    if (statusFilter) {
        filteredPOs = filteredPOs.filter(po => po.status === statusFilter);
    }
    
    if (customerFilter) {
        filteredPOs = filteredPOs.filter(po => po.customerType === customerFilter);
    }
    
    // Update table with filtered results
    const tbody = document.getElementById('poTableBody');
    if (!tbody) return;
    
    tbody.innerHTML = filteredPOs.map(po => `
        <tr ${po.status === 'pending' ? 'style="background-color: #fef3c7; border-left: 4px solid #f59e0b;"' : ''}>
            <td>${po.id}</td>
            <td>${po.customerName}</td>
            <td class="customer-type-badge customer-type-${po.customerType.toLowerCase()}">${po.customerType}</td>
            <td style="font-weight: ${po.status === 'pending' ? 'bold' : 'normal'}; color: ${po.status === 'pending' ? '#d97706' : 'inherit'};">
                $${po.amount.toLocaleString()}
                ${po.status === 'pending' ? '<br><small style="color: #92400e;">⏳ Awaiting Approval</small>' : ''}
            </td>
            <td><span class="status-badge status-${po.status}">${PO_STATUSES[po.status]}</span></td>
            <td>${formatDate(po.created)}</td>
            <td>
                <button class="action-btn action-view" onclick="viewPO('${po.id}')">View</button>
                ${auth.canWrite() ? `<button class="action-btn action-edit" onclick="editPO('${po.id}')">Edit</button>` : ''}
                ${auth.canApprove() && po.status === 'pending' ? `
                    <button class="action-btn action-approve" onclick="approvePO('${po.id}')">Approve</button>
                    <button class="action-btn action-reject" onclick="rejectPO('${po.id}')">Reject</button>
                ` : ''}
            </td>
        </tr>
    `).join('');
}

// Settings functionality
function saveSettings() {
    const defaultPlatform = document.getElementById('defaultPlatform').value;
    const approvalRequired = document.getElementById('approvalRequired').checked;
    
    mockData.settings.defaultPlatform = defaultPlatform;
    mockData.settings.approvalRequired = approvalRequired;
    
    saveToLocalStorage();
    showNotification('Settings saved successfully!', 'success');
}
